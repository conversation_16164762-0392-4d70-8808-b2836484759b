package:
  name: hwpe-ctrl
  authors:
    - "<PERSON> <<EMAIL>>"

dependencies:
  tech_cells_generic: { git: "https://github.com/pulp-platform/tech_cells_generic.git", version: 0.2.2 }

sources:
  - include_dirs:
      - rtl
    files:
      # Source files grouped in levels. Files in level 0 have no dependencies on files in this
      # package. Files in level 1 only depend on files in level 0, files in level 2 on files in
      # levels 1 and 0, etc. Files within a level are ordered alphabetically.
      # Level 0
      - rtl/hwpe_ctrl_package.sv
      - rtl/hwpe_ctrl_interfaces.sv
      # Level 1
      - rtl/hwpe_ctrl_regfile_ff.sv
      - rtl/hwpe_ctrl_regfile_latch.sv
      - rtl/hwpe_ctrl_seq_mult.sv
      - rtl/hwpe_ctrl_uloop.sv
      # Level 2
      - rtl/hwpe_ctrl_regfile_latch_test_wrap.sv
      # Level 3
      - rtl/hwpe_ctrl_regfile.sv
      # Level 4
      - rtl/hwpe_ctrl_slave.sv
