add_rules("mode.debug", "mode.release")

target("mac_engine")
    add_rules("verilator.static")
    set_toolchains("@verilator")
    add_values("verilator.flags","-I./hwpe-stream/rtl","-I./hwpe-mac-engine/rtl","-I./hwpe-stream/rtl/hwpe_stream_interfaces.sv","--hierarchical","--trace")
    add_values("verilator.flags","--structs-packed","--trace-structs")
    add_files("hwpe-stream/rtl/hwpe_stream_interfaces.sv")
    add_files("hwpe-mac-engine/rtl/mac_engine.sv")
    add_files("hwpe-mac-engine/rtl/mac_engine_wrapper.sv")


target("test_mac_top")
    add_rules("verilator.binary")
    set_toolchains("@verilator")
    add_deps("mac_engine")
    add_files("hwpe-mac-engine/test/test_mac_engine.cpp")