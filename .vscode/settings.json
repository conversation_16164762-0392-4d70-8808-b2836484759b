{
    "verilog.includes": [
        "hci",
        "hwpe-ctrl",
        "hwpe-mac-engine",
        "hwpe-stream",
        "sim_build",
        "tech_cells_generic"
    ],
    "verilog.includeGlobs": [
        "**/*.{svh}",
        "**/*.{sv}",
        "**/*.{v}"
    ],
    // "verilog.moduleGlobs": [
    //     "**/*.{sv,v}",
    //     "**/*.{svh}"
    // ],
    "verilog.moduleGlobs": [
        "hwpe-stream/rtl/hwpe_stream_package.sv",
        "hwpe-stream/rtl/hwpe_stream_interfaces.sv",
        "**/*.{svh}",
        "**/*.{sv}",
        "*.{sv}",
        "*.{svh}"
    ]
}