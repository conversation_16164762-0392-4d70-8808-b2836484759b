// Copyright lowRISC contributors.
// Licensed under the Apache License, Version 2.0, see LICENSE for details.
// SPDX-License-Identifier: Apache-2.0
//
// Register Top module auto-generated by `reggen`


`include "common_cells/assertions.svh"

module hci_ecc_manager_reg_top #(
  parameter type reg_req_t = logic,
  parameter type reg_rsp_t = logic,
  parameter int AW = 4
) (
  input logic clk_i,
  input logic rst_ni,
  input  reg_req_t reg_req_i,
  output reg_rsp_t reg_rsp_o,
  // To HW
  output hci_ecc_manager_reg_pkg::hci_ecc_manager_reg2hw_t reg2hw, // Write
  input  hci_ecc_manager_reg_pkg::hci_ecc_manager_hw2reg_t hw2reg, // Read


  // Config
  input devmode_i // If 1, explicit error return for unmapped register access
);

  import hci_ecc_manager_reg_pkg::* ;

  localparam int DW = 32;
  localparam int DBW = DW/8;                    // Byte Width

  // register signals
  logic           reg_we;
  logic           reg_re;
  logic [BlockAw-1:0]  reg_addr;
  logic [DW-1:0]  reg_wdata;
  logic [DBW-1:0] reg_be;
  logic [DW-1:0]  reg_rdata;
  logic           reg_error;

  logic          addrmiss, wr_err;

  logic [DW-1:0] reg_rdata_next;

  // Below register interface can be changed
  reg_req_t  reg_intf_req;
  reg_rsp_t  reg_intf_rsp;


  assign reg_intf_req = reg_req_i;
  assign reg_rsp_o = reg_intf_rsp;


  assign reg_we = reg_intf_req.valid & reg_intf_req.write;
  assign reg_re = reg_intf_req.valid & ~reg_intf_req.write;
  assign reg_addr = reg_intf_req.addr[BlockAw-1:0];
  assign reg_wdata = reg_intf_req.wdata;
  assign reg_be = reg_intf_req.wstrb;
  assign reg_intf_rsp.rdata = reg_rdata;
  assign reg_intf_rsp.error = reg_error;
  assign reg_intf_rsp.ready = 1'b1;

  assign reg_rdata = reg_rdata_next ;
  assign reg_error = (devmode_i & addrmiss) | wr_err;


  // Define SW related signals
  // Format: <reg>_<field>_{wd|we|qs}
  //        or <reg>_{wd|we|qs} if field == 1 or 0
  logic [31:0] data_correctable_errors_qs;
  logic [31:0] data_correctable_errors_wd;
  logic data_correctable_errors_we;
  logic [31:0] data_uncorrectable_errors_qs;
  logic [31:0] data_uncorrectable_errors_wd;
  logic data_uncorrectable_errors_we;
  logic [31:0] metadata_correctable_errors_qs;
  logic [31:0] metadata_correctable_errors_wd;
  logic metadata_correctable_errors_we;
  logic [31:0] metadata_uncorrectable_errors_qs;
  logic [31:0] metadata_uncorrectable_errors_wd;
  logic metadata_uncorrectable_errors_we;

  // Register instances
  // R[data_correctable_errors]: V(False)

  prim_subreg #(
    .DW      (32),
    .SWACCESS("W0C"),
    .RESVAL  (32'h0)
  ) u_data_correctable_errors (
    .clk_i   (clk_i    ),
    .rst_ni  (rst_ni  ),

    // from register interface
    .we     (data_correctable_errors_we),
    .wd     (data_correctable_errors_wd),

    // from internal hardware
    .de     (hw2reg.data_correctable_errors.de),
    .d      (hw2reg.data_correctable_errors.d ),

    // to internal hardware
    .qe     (),
    .q      (reg2hw.data_correctable_errors.q ),

    // to register interface (read)
    .qs     (data_correctable_errors_qs)
  );


  // R[data_uncorrectable_errors]: V(False)

  prim_subreg #(
    .DW      (32),
    .SWACCESS("W0C"),
    .RESVAL  (32'h0)
  ) u_data_uncorrectable_errors (
    .clk_i   (clk_i    ),
    .rst_ni  (rst_ni  ),

    // from register interface
    .we     (data_uncorrectable_errors_we),
    .wd     (data_uncorrectable_errors_wd),

    // from internal hardware
    .de     (hw2reg.data_uncorrectable_errors.de),
    .d      (hw2reg.data_uncorrectable_errors.d ),

    // to internal hardware
    .qe     (),
    .q      (reg2hw.data_uncorrectable_errors.q ),

    // to register interface (read)
    .qs     (data_uncorrectable_errors_qs)
  );


  // R[metadata_correctable_errors]: V(False)

  prim_subreg #(
    .DW      (32),
    .SWACCESS("W0C"),
    .RESVAL  (32'h0)
  ) u_metadata_correctable_errors (
    .clk_i   (clk_i    ),
    .rst_ni  (rst_ni  ),

    // from register interface
    .we     (metadata_correctable_errors_we),
    .wd     (metadata_correctable_errors_wd),

    // from internal hardware
    .de     (hw2reg.metadata_correctable_errors.de),
    .d      (hw2reg.metadata_correctable_errors.d ),

    // to internal hardware
    .qe     (),
    .q      (reg2hw.metadata_correctable_errors.q ),

    // to register interface (read)
    .qs     (metadata_correctable_errors_qs)
  );


  // R[metadata_uncorrectable_errors]: V(False)

  prim_subreg #(
    .DW      (32),
    .SWACCESS("W0C"),
    .RESVAL  (32'h0)
  ) u_metadata_uncorrectable_errors (
    .clk_i   (clk_i    ),
    .rst_ni  (rst_ni  ),

    // from register interface
    .we     (metadata_uncorrectable_errors_we),
    .wd     (metadata_uncorrectable_errors_wd),

    // from internal hardware
    .de     (hw2reg.metadata_uncorrectable_errors.de),
    .d      (hw2reg.metadata_uncorrectable_errors.d ),

    // to internal hardware
    .qe     (),
    .q      (reg2hw.metadata_uncorrectable_errors.q ),

    // to register interface (read)
    .qs     (metadata_uncorrectable_errors_qs)
  );




  logic [3:0] addr_hit;
  always_comb begin
    addr_hit = '0;
    addr_hit[0] = (reg_addr == HCI_ECC_MANAGER_DATA_CORRECTABLE_ERRORS_OFFSET);
    addr_hit[1] = (reg_addr == HCI_ECC_MANAGER_DATA_UNCORRECTABLE_ERRORS_OFFSET);
    addr_hit[2] = (reg_addr == HCI_ECC_MANAGER_METADATA_CORRECTABLE_ERRORS_OFFSET);
    addr_hit[3] = (reg_addr == HCI_ECC_MANAGER_METADATA_UNCORRECTABLE_ERRORS_OFFSET);
  end

  assign addrmiss = (reg_re || reg_we) ? ~|addr_hit : 1'b0 ;

  // Check sub-word write is permitted
  always_comb begin
    wr_err = (reg_we &
              ((addr_hit[0] & (|(HCI_ECC_MANAGER_PERMIT[0] & ~reg_be))) |
               (addr_hit[1] & (|(HCI_ECC_MANAGER_PERMIT[1] & ~reg_be))) |
               (addr_hit[2] & (|(HCI_ECC_MANAGER_PERMIT[2] & ~reg_be))) |
               (addr_hit[3] & (|(HCI_ECC_MANAGER_PERMIT[3] & ~reg_be)))));
  end

  assign data_correctable_errors_we = addr_hit[0] & reg_we & !reg_error;
  assign data_correctable_errors_wd = reg_wdata[31:0];

  assign data_uncorrectable_errors_we = addr_hit[1] & reg_we & !reg_error;
  assign data_uncorrectable_errors_wd = reg_wdata[31:0];

  assign metadata_correctable_errors_we = addr_hit[2] & reg_we & !reg_error;
  assign metadata_correctable_errors_wd = reg_wdata[31:0];

  assign metadata_uncorrectable_errors_we = addr_hit[3] & reg_we & !reg_error;
  assign metadata_uncorrectable_errors_wd = reg_wdata[31:0];

  // Read data return
  always_comb begin
    reg_rdata_next = '0;
    unique case (1'b1)
      addr_hit[0]: begin
        reg_rdata_next[31:0] = data_correctable_errors_qs;
      end

      addr_hit[1]: begin
        reg_rdata_next[31:0] = data_uncorrectable_errors_qs;
      end

      addr_hit[2]: begin
        reg_rdata_next[31:0] = metadata_correctable_errors_qs;
      end

      addr_hit[3]: begin
        reg_rdata_next[31:0] = metadata_uncorrectable_errors_qs;
      end

      default: begin
        reg_rdata_next = '1;
      end
    endcase
  end

  // Unused signal tieoff

  // wdata / byte enable are not always fully used
  // add a blanket unused statement to handle lint waivers
  logic unused_wdata;
  logic unused_be;
  assign unused_wdata = ^reg_wdata;
  assign unused_be = ^reg_be;

  // Assertions for Register Interface
  `ASSERT(en2addrHit, (reg_we || reg_re) |-> $onehot0(addr_hit))

endmodule

module hci_ecc_manager_reg_top_intf
#(
  parameter int AW = 4,
  localparam int DW = 32
) (
  input logic clk_i,
  input logic rst_ni,
  REG_BUS.in  regbus_slave,
  // To HW
  output hci_ecc_manager_reg_pkg::hci_ecc_manager_reg2hw_t reg2hw, // Write
  input  hci_ecc_manager_reg_pkg::hci_ecc_manager_hw2reg_t hw2reg, // Read
  // Config
  input devmode_i // If 1, explicit error return for unmapped register access
);
 localparam int unsigned STRB_WIDTH = DW/8;

`include "register_interface/typedef.svh"
`include "register_interface/assign.svh"

  // Define structs for reg_bus
  typedef logic [AW-1:0] addr_t;
  typedef logic [DW-1:0] data_t;
  typedef logic [STRB_WIDTH-1:0] strb_t;
  `REG_BUS_TYPEDEF_ALL(reg_bus, addr_t, data_t, strb_t)

  reg_bus_req_t s_reg_req;
  reg_bus_rsp_t s_reg_rsp;
  
  // Assign SV interface to structs
  `REG_BUS_ASSIGN_TO_REQ(s_reg_req, regbus_slave)
  `REG_BUS_ASSIGN_FROM_RSP(regbus_slave, s_reg_rsp)

  

  hci_ecc_manager_reg_top #(
    .reg_req_t(reg_bus_req_t),
    .reg_rsp_t(reg_bus_rsp_t),
    .AW(AW)
  ) i_regs (
    .clk_i,
    .rst_ni,
    .reg_req_i(s_reg_req),
    .reg_rsp_o(s_reg_rsp),
    .reg2hw, // Write
    .hw2reg, // Read
    .devmode_i
  );
  
endmodule


