hci:
  vlog_opts: [
    +nowarn<PERSON><PERSON><PERSON>,
    -suppress 2275,
    -L hwpe_stream_lib,
    -L cluster_interconnect_lib,
  ]
  jg_inclibs: [
    hwpe-stream,
  ]
  incdirs: [
    rtl/common,
  ]
  files:
    # Level 0
    - rtl/common/hci_package.sv
    # Level 1
    - rtl/common/hci_interfaces.sv
    - rtl/core/hci_core_assign.sv
    - rtl/core/hci_core_fifo.sv
    - rtl/core/hci_core_mux_dynamic.sv
    - rtl/core/hci_core_mux_static.sv
    - rtl/core/hci_core_mux_ooo.sv
    - rtl/core/hci_core_r_valid_filter.sv
    - rtl/core/hci_core_r_id_filter.sv
    - rtl/core/hci_core_source.sv
    - rtl/core/hci_core_split.sv
    # - rtl/interco/hci_log_interconnect.sv
    - rtl/interco/hci_log_interconnect_l2.sv
    - rtl/interco/hci_new_log_interconnect.sv # `new_XBAR_TCDM` dep. is a private repo
    - rtl/interco/hci_arbiter.sv
    - rtl/interco/hci_router_reorder.sv
    - rtl/parity/hci_copy_source.sv
    - rtl/parity/hci_copy_sink.sv
    # Level 3
    - rtl/core/hci_core_sink.sv
    - rtl/interco/hci_router.sv
    # Level 4
    - rtl/hci_interconnect.sv
