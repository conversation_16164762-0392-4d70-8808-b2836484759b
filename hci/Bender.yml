package:
  name: hci
  authors:
    - "<PERSON> <<EMAIL>>"
    - "<PERSON><PERSON><PERSON> <<EMAIL>"
    - "<PERSON> <to<PERSON><PERSON><PERSON>@student.ethz.ch>"
    - "<PERSON> <<EMAIL>>"
    - "<PERSON><PERSON><PERSON> <<EMAIL>>"

dependencies:
  hwpe-stream:            { git: "https://github.com/pulp-platform/hwpe-stream.git", version: 1.9.0 }
  cluster_interconnect:   { git: "https://github.com/pulp-platform/cluster_interconnect.git", rev: 1284def6c0b7f7e9355eb093d00883ad9dead1b7 }
  L2_tcdm_hybrid_interco: { git: "https://github.com/pulp-platform/L2_tcdm_hybrid_interco.git", version: 1.0.0 }
  redundancy_cells:       { git: "https://github.com/pulp-platform/redundancy_cells.git", rev: c37bdb47339bf70e8323de8df14ea8bbeafb6583 } # branch: astral_rebase
  common_cells:           { git: "https://github.com/pulp-platform/common_cells.git", version: 1.24.0 }
  register_interface:     { git: "https://github.com/pulp-platform/register_interface.git", version: 0.3.1 }

# this is necessary to expose hci_helpers.svh to dependent packages
export_include_dirs:
  - rtl/common

sources:
  - include_dirs:
      - rtl/common
    files:
      # Source files grouped in levels. Files in level 0 have no dependencies on files in this
      # package. Files in level 1 only depend on files in level 0, files in level 2 on files in
      # levels 1 and 0, etc. Files within a level are ordered alphabetically.
      # Level 0
      - rtl/common/hci_package.sv
      - rtl/ecc/hci_ecc_manager_reg_pkg.sv
      # Level 1
      - rtl/common/hci_interfaces.sv
      - rtl/ecc/hci_ecc_manager_reg_top.sv
      # Level 2
      - rtl/core/hci_core_assign.sv
      - rtl/core/hci_core_assign_expand.sv
      - rtl/core/hci_core_fifo.sv
      - rtl/core/hci_core_mux_dynamic.sv
      - rtl/core/hci_core_mux_static.sv
      - rtl/core/hci_core_mux_ooo.sv
      - rtl/core/hci_core_r_valid_filter.sv
      - rtl/core/hci_core_r_id_filter.sv
      - rtl/core/hci_core_source.sv
      - rtl/core/hci_core_split.sv
      - rtl/ecc/hci_ecc_dec.sv
      - rtl/ecc/hci_ecc_enc.sv
      - rtl/ecc/hci_ecc_manager.sv
      - rtl/interco/hci_log_interconnect.sv
      - rtl/interco/hci_log_interconnect_l2.sv
      - rtl/interco/hci_new_log_interconnect.sv # `new_XBAR_TCDM` dep. is a private repo
      - rtl/interco/hci_arbiter.sv
      - rtl/interco/hci_arbiter_tree.sv
      - rtl/interco/hci_router_reorder.sv
      - rtl/parity/hci_copy_source.sv
      - rtl/parity/hci_copy_sink.sv
      # Level 3
      - rtl/core/hci_core_sink.sv
      - rtl/ecc/hci_ecc_source.sv
      - rtl/interco/hci_router.sv
      # Level 4
      - rtl/ecc/hci_ecc_interconnect.sv
      - rtl/ecc/hci_ecc_sink.sv
      - rtl/hci_interconnect.sv
