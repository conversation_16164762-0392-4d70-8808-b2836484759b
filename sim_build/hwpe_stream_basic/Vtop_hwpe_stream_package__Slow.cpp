// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Design implementation internals
// See Vtop.h for the primary calling header

#include "Vtop__pch.h"
#include "Vtop__Syms.h"
#include "Vtop_hwpe_stream_package.h"

// Parameter definitions for Vtop_hwpe_stream_package
constexpr CData/*1:0*/ Vtop_hwpe_stream_package::HWPE_STREAM_ADDRESSGEN_3D;
constexpr CData/*1:0*/ Vtop_hwpe_stream_package::HWPE_STREAM_ADDRESSGEN_2D;
constexpr CData/*1:0*/ Vtop_hwpe_stream_package::HWPE_STREAM_ADDRESSGEN_1D;
constexpr IData/*31:0*/ Vtop_hwpe_stream_package::HWPE_STREAM_REALIGN_SOURCE;
constexpr IData/*31:0*/ Vtop_hwpe_stream_package::HWPE_STREAM_REALIGN_SINK;
constexpr IData/*31:0*/ Vtop_hwpe_stream_package::NB_SERDES_STREAMS_MAX;


void Vtop_hwpe_stream_package___ctor_var_reset(Vtop_hwpe_stream_package* vlSelf);

Vtop_hwpe_stream_package::Vtop_hwpe_stream_package(Vtop__Syms* symsp, const char* v__name)
    : VerilatedModule{v__name}
    , vlSymsp{symsp}
 {
    // Reset structure values
    Vtop_hwpe_stream_package___ctor_var_reset(this);
}

void Vtop_hwpe_stream_package::__Vconfigure(bool first) {
    (void)first;  // Prevent unused variable warning
}

Vtop_hwpe_stream_package::~Vtop_hwpe_stream_package() {
}
