// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Design implementation internals
// See Vtop.h for the primary calling header

#include "Vtop__pch.h"
#include "Vtop_hwpe_stream_intf_stream__D20.h"

std::string VL_TO_STRING(const Vtop_hwpe_stream_intf_stream__D20* obj) {
    VL_DEBUG_IF(VL_DBG_MSGF("+          Vtop_hwpe_stream_intf_stream__D20::VL_TO_STRING\n"); );
    // Body
    return (obj ? obj->name() : "null");
}
