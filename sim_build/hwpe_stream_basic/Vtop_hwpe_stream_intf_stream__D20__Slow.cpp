// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Design implementation internals
// See Vtop.h for the primary calling header

#include "Vtop__pch.h"
#include "Vtop__Syms.h"
#include "Vtop_hwpe_stream_intf_stream__D20.h"

// Parameter definitions for Vtop_hwpe_stream_intf_stream__D20
constexpr CData/*0:0*/ Vtop_hwpe_stream_intf_stream__D20::BYPASS_VCR_ASSERT;
constexpr CData/*0:0*/ Vtop_hwpe_stream_intf_stream__D20::BYPASS_VDR_ASSERT;
constexpr IData/*31:0*/ Vtop_hwpe_stream_intf_stream__D20::DATA_WIDTH;
constexpr IData/*31:0*/ Vtop_hwpe_stream_intf_stream__D20::STRB_WIDTH;


void Vtop_hwpe_stream_intf_stream__D20___ctor_var_reset(Vtop_hwpe_stream_intf_stream__D20* vlSelf);

Vtop_hwpe_stream_intf_stream__D20::Vtop_hwpe_stream_intf_stream__D20(Vtop__Syms* symsp, const char* v__name)
    : VerilatedModule{v__name}
    , vlSymsp{symsp}
 {
    // Reset structure values
    Vtop_hwpe_stream_intf_stream__D20___ctor_var_reset(this);
}

void Vtop_hwpe_stream_intf_stream__D20::__Vconfigure(bool first) {
    (void)first;  // Prevent unused variable warning
}

Vtop_hwpe_stream_intf_stream__D20::~Vtop_hwpe_stream_intf_stream__D20() {
}
