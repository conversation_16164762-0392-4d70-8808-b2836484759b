<testsuites name="results">
  <testsuite name="all" package="all">
    <property name="random_seed" value="1750340120" />
    <testcase name="test_simple_stream_transfer" classname="test_hwpe_stream_basic" file="/home/<USER>/asic_learn/hwpe-stream/verification/test_hwpe_stream_basic.py" lineno="86" time="0.0025644302368164062" sim_time_ns="110.001" ratio_time="42894.90835849758">
      <failure message="Test failed with RANDOM_SEED=1750340120" />
    </testcase>
    <testcase name="test_stream_backpressure" classname="test_hwpe_stream_basic" file="/home/<USER>/asic_learn/hwpe-stream/verification/test_hwpe_stream_basic.py" lineno="155" time="0.002182483673095703" sim_time_ns="120.001" ratio_time="54983.68738300197">
      <failure message="Test failed with RANDOM_SEED=1750340120" />
    </testcase>
    <testcase name="test_stream_burst_transfer" classname="test_hwpe_stream_basic" file="/home/<USER>/asic_learn/hwpe-stream/verification/test_hwpe_stream_basic.py" lineno="223" time="0.0020418167114257812" sim_time_ns="120.00099999999998" ratio_time="58771.68079215319">
      <failure message="Test failed with RANDOM_SEED=1750340120" />
    </testcase>
    <testcase name="test_stream_strobe_patterns" classname="test_hwpe_stream_basic" file="/home/<USER>/asic_learn/hwpe-stream/verification/test_hwpe_stream_basic.py" lineno="288" time="0.0017037391662597656" sim_time_ns="120.00100000000003" ratio_time="70433.9034850266">
      <failure message="Test failed with RANDOM_SEED=1750340120" />
    </testcase>
  </testsuite>
</testsuites>
