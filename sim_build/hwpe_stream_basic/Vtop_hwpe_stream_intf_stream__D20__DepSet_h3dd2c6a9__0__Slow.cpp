// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Design implementation internals
// See Vtop.h for the primary calling header

#include "Vtop__pch.h"
#include "Vtop_hwpe_stream_intf_stream__D20.h"

VL_ATTR_COLD void Vtop_hwpe_stream_intf_stream__D20___ctor_var_reset(Vtop_hwpe_stream_intf_stream__D20* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+          Vtop_hwpe_stream_intf_stream__D20___ctor_var_reset\n"); );
    Vtop__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    vlSelf->clk = VL_RAND_RESET_I(1);
    vlSelf->valid = VL_RAND_RESET_I(1);
    vlSelf->ready = VL_RAND_RESET_I(1);
    vlSelf->data = VL_RAND_RESET_I(32);
    vlSelf->strb = VL_RAND_RESET_I(4);
}
