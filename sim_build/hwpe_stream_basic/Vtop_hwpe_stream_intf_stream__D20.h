// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Design internal header
// See Vtop.h for the primary calling header

#ifndef VERILATED_VTOP_HWPE_STREAM_INTF_STREAM__D20_H_
#define VERILATED_VTOP_HWPE_STREAM_INTF_STREAM__D20_H_  // guard

#include "verilated.h"


class Vtop__Syms;

class alignas(VL_CACHE_LINE_BYTES) Vtop_hwpe_stream_intf_stream__D20 final : public VerilatedModule {
  public:

    // DESIGN SPECIFIC STATE
    VL_IN8(clk,0,0);
    CData/*0:0*/ valid;
    CData/*0:0*/ ready;
    CData/*3:0*/ strb;
    IData/*31:0*/ data;

    // INTERNAL VARIABLES
    Vtop__Syms* const vlSymsp;

    // PARAMETERS
    static constexpr CData/*0:0*/ BYPASS_VCR_ASSERT = 0U;
    static constexpr CData/*0:0*/ BYPASS_VDR_ASSERT = 0U;
    static constexpr IData/*31:0*/ DATA_WIDTH = 0x00000020U;
    static constexpr IData/*31:0*/ STRB_WIDTH = 4U;

    // CONSTRUCTORS
    Vtop_hwpe_stream_intf_stream__D20(Vtop__Syms* symsp, const char* v__name);
    ~Vtop_hwpe_stream_intf_stream__D20();
    VL_UNCOPYABLE(Vtop_hwpe_stream_intf_stream__D20);

    // INTERNAL METHODS
    void __Vconfigure(bool first);
};

std::string VL_TO_STRING(const Vtop_hwpe_stream_intf_stream__D20* obj);

#endif  // guard
