// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Design implementation internals
// See Vtop.h for the primary calling header

#include "Vtop__pch.h"
#include "Vtop___024root.h"

VL_ATTR_COLD void Vtop___024root___eval_static(Vtop___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vtop___024root___eval_static\n"); );
    Vtop__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    vlSelfRef.__Vtrigprevexpr___TOP__hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__clk_gated__0 
        = vlSelfRef.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__clk_gated;
    vlSelfRef.__Vtrigprevexpr___TOP__rst_ni__0 = vlSelfRef.rst_ni;
}

VL_ATTR_COLD void Vtop___024root___eval_initial(Vtop___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vtop___024root___eval_initial\n"); );
    Vtop__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
}

VL_ATTR_COLD void Vtop___024root___eval_final(Vtop___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vtop___024root___eval_final\n"); );
    Vtop__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
}

#ifdef VL_DEBUG
VL_ATTR_COLD void Vtop___024root___dump_triggers__stl(Vtop___024root* vlSelf);
#endif  // VL_DEBUG
VL_ATTR_COLD bool Vtop___024root___eval_phase__stl(Vtop___024root* vlSelf);

VL_ATTR_COLD void Vtop___024root___eval_settle(Vtop___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vtop___024root___eval_settle\n"); );
    Vtop__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Init
    IData/*31:0*/ __VstlIterCount;
    CData/*0:0*/ __VstlContinue;
    // Body
    __VstlIterCount = 0U;
    vlSelfRef.__VstlFirstIteration = 1U;
    __VstlContinue = 1U;
    while (__VstlContinue) {
        if (VL_UNLIKELY(((0x64U < __VstlIterCount)))) {
#ifdef VL_DEBUG
            Vtop___024root___dump_triggers__stl(vlSelf);
#endif
            VL_FATAL_MT("/home/<USER>/asic_learn/hwpe-stream/verification/hwpe_stream_buffer_wrapper.sv", 8, "", "Settle region did not converge.");
        }
        __VstlIterCount = ((IData)(1U) + __VstlIterCount);
        __VstlContinue = 0U;
        if (Vtop___024root___eval_phase__stl(vlSelf)) {
            __VstlContinue = 1U;
        }
        vlSelfRef.__VstlFirstIteration = 0U;
    }
}

#ifdef VL_DEBUG
VL_ATTR_COLD void Vtop___024root___dump_triggers__stl(Vtop___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vtop___024root___dump_triggers__stl\n"); );
    Vtop__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    if ((1U & (~ vlSelfRef.__VstlTriggered.any()))) {
        VL_DBG_MSGF("         No triggers active\n");
    }
    if ((1ULL & vlSelfRef.__VstlTriggered.word(0U))) {
        VL_DBG_MSGF("         'stl' region trigger index 0 is active: Internal 'stl' trigger - first iteration\n");
    }
}
#endif  // VL_DEBUG

void Vtop___024root___ico_sequent__TOP__0(Vtop___024root* vlSelf);

VL_ATTR_COLD void Vtop___024root___eval_stl(Vtop___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vtop___024root___eval_stl\n"); );
    Vtop__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    if ((1ULL & vlSelfRef.__VstlTriggered.word(0U))) {
        Vtop___024root___ico_sequent__TOP__0(vlSelf);
    }
}

VL_ATTR_COLD void Vtop___024root___eval_triggers__stl(Vtop___024root* vlSelf);

VL_ATTR_COLD bool Vtop___024root___eval_phase__stl(Vtop___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vtop___024root___eval_phase__stl\n"); );
    Vtop__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Init
    CData/*0:0*/ __VstlExecute;
    // Body
    Vtop___024root___eval_triggers__stl(vlSelf);
    __VstlExecute = vlSelfRef.__VstlTriggered.any();
    if (__VstlExecute) {
        Vtop___024root___eval_stl(vlSelf);
    }
    return (__VstlExecute);
}

#ifdef VL_DEBUG
VL_ATTR_COLD void Vtop___024root___dump_triggers__ico(Vtop___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vtop___024root___dump_triggers__ico\n"); );
    Vtop__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    if ((1U & (~ vlSelfRef.__VicoTriggered.any()))) {
        VL_DBG_MSGF("         No triggers active\n");
    }
    if ((1ULL & vlSelfRef.__VicoTriggered.word(0U))) {
        VL_DBG_MSGF("         'ico' region trigger index 0 is active: Internal 'ico' trigger - first iteration\n");
    }
}
#endif  // VL_DEBUG

#ifdef VL_DEBUG
VL_ATTR_COLD void Vtop___024root___dump_triggers__act(Vtop___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vtop___024root___dump_triggers__act\n"); );
    Vtop__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    if ((1U & (~ vlSelfRef.__VactTriggered.any()))) {
        VL_DBG_MSGF("         No triggers active\n");
    }
    if ((1ULL & vlSelfRef.__VactTriggered.word(0U))) {
        VL_DBG_MSGF("         'act' region trigger index 0 is active: @(posedge hwpe_stream_buffer_wrapper.i_hwpe_stream_buffer.clk_gated)\n");
    }
    if ((2ULL & vlSelfRef.__VactTriggered.word(0U))) {
        VL_DBG_MSGF("         'act' region trigger index 1 is active: @(negedge rst_ni)\n");
    }
}
#endif  // VL_DEBUG

#ifdef VL_DEBUG
VL_ATTR_COLD void Vtop___024root___dump_triggers__nba(Vtop___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vtop___024root___dump_triggers__nba\n"); );
    Vtop__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    if ((1U & (~ vlSelfRef.__VnbaTriggered.any()))) {
        VL_DBG_MSGF("         No triggers active\n");
    }
    if ((1ULL & vlSelfRef.__VnbaTriggered.word(0U))) {
        VL_DBG_MSGF("         'nba' region trigger index 0 is active: @(posedge hwpe_stream_buffer_wrapper.i_hwpe_stream_buffer.clk_gated)\n");
    }
    if ((2ULL & vlSelfRef.__VnbaTriggered.word(0U))) {
        VL_DBG_MSGF("         'nba' region trigger index 1 is active: @(negedge rst_ni)\n");
    }
}
#endif  // VL_DEBUG

VL_ATTR_COLD void Vtop___024root___ctor_var_reset(Vtop___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vtop___024root___ctor_var_reset\n"); );
    Vtop__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    vlSelf->clk_i = VL_RAND_RESET_I(1);
    vlSelf->rst_ni = VL_RAND_RESET_I(1);
    vlSelf->clear_i = VL_RAND_RESET_I(1);
    vlSelf->test_mode_i = VL_RAND_RESET_I(1);
    vlSelf->push_i_valid = VL_RAND_RESET_I(1);
    vlSelf->push_i_data = VL_RAND_RESET_I(32);
    vlSelf->push_i_strb = VL_RAND_RESET_I(4);
    vlSelf->push_i_ready = VL_RAND_RESET_I(1);
    vlSelf->pop_o_valid = VL_RAND_RESET_I(1);
    vlSelf->pop_o_data = VL_RAND_RESET_I(32);
    vlSelf->pop_o_strb = VL_RAND_RESET_I(4);
    vlSelf->pop_o_ready = VL_RAND_RESET_I(1);
    vlSelf->hwpe_stream_buffer_wrapper__DOT__clk_i = VL_RAND_RESET_I(1);
    vlSelf->hwpe_stream_buffer_wrapper__DOT__rst_ni = VL_RAND_RESET_I(1);
    vlSelf->hwpe_stream_buffer_wrapper__DOT__clear_i = VL_RAND_RESET_I(1);
    vlSelf->hwpe_stream_buffer_wrapper__DOT__test_mode_i = VL_RAND_RESET_I(1);
    vlSelf->hwpe_stream_buffer_wrapper__DOT__push_i_valid = VL_RAND_RESET_I(1);
    vlSelf->hwpe_stream_buffer_wrapper__DOT__push_i_data = VL_RAND_RESET_I(32);
    vlSelf->hwpe_stream_buffer_wrapper__DOT__push_i_strb = VL_RAND_RESET_I(4);
    vlSelf->hwpe_stream_buffer_wrapper__DOT__push_i_ready = VL_RAND_RESET_I(1);
    vlSelf->hwpe_stream_buffer_wrapper__DOT__pop_o_valid = VL_RAND_RESET_I(1);
    vlSelf->hwpe_stream_buffer_wrapper__DOT__pop_o_data = VL_RAND_RESET_I(32);
    vlSelf->hwpe_stream_buffer_wrapper__DOT__pop_o_strb = VL_RAND_RESET_I(4);
    vlSelf->hwpe_stream_buffer_wrapper__DOT__pop_o_ready = VL_RAND_RESET_I(1);
    vlSelf->hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__clk_i = VL_RAND_RESET_I(1);
    vlSelf->hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__rst_ni = VL_RAND_RESET_I(1);
    vlSelf->hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__clear_i = VL_RAND_RESET_I(1);
    vlSelf->hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__test_mode_i = VL_RAND_RESET_I(1);
    vlSelf->hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__clk_gated = VL_RAND_RESET_I(1);
    vlSelf->hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__i_cg__DOT__clk_i = VL_RAND_RESET_I(1);
    vlSelf->hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__i_cg__DOT__en_i = VL_RAND_RESET_I(1);
    vlSelf->hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__i_cg__DOT__test_en_i = VL_RAND_RESET_I(1);
    vlSelf->hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__i_cg__DOT__clk_o = VL_RAND_RESET_I(1);
    vlSelf->hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__i_cg__DOT__clk_en = VL_RAND_RESET_I(1);
    vlSelf->__Vtrigprevexpr___TOP__hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__clk_gated__0 = VL_RAND_RESET_I(1);
    vlSelf->__Vtrigprevexpr___TOP__rst_ni__0 = VL_RAND_RESET_I(1);
}
