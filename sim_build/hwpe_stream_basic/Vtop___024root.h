// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Design internal header
// See Vtop.h for the primary calling header

#ifndef VERILATED_VTOP___024ROOT_H_
#define VERILATED_VTOP___024ROOT_H_  // guard

#include "verilated.h"
class Vtop_hwpe_stream_intf_stream__D20;
class Vtop_hwpe_stream_package;


class Vtop__Syms;

class alignas(VL_CACHE_LINE_BYTES) Vtop___024root final : public VerilatedModule {
  public:
    // CELLS
    Vtop_hwpe_stream_package* __PVT__hwpe_stream_package;
    Vtop_hwpe_stream_intf_stream__D20* __PVT__hwpe_stream_buffer_wrapper__DOT__push_intf_i;
    Vtop_hwpe_stream_intf_stream__D20* __PVT__hwpe_stream_buffer_wrapper__DOT__pop_intf_o;

    // DESIGN SPECIFIC STATE
    VL_IN8(rst_ni,0,0);
    CData/*0:0*/ hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__clk_gated;
    VL_IN8(clk_i,0,0);
    VL_IN8(clear_i,0,0);
    VL_IN8(test_mode_i,0,0);
    VL_IN8(push_i_valid,0,0);
    VL_IN8(push_i_strb,3,0);
    VL_OUT8(push_i_ready,0,0);
    VL_OUT8(pop_o_valid,0,0);
    VL_OUT8(pop_o_strb,3,0);
    VL_IN8(pop_o_ready,0,0);
    CData/*0:0*/ hwpe_stream_buffer_wrapper__DOT__clk_i;
    CData/*0:0*/ hwpe_stream_buffer_wrapper__DOT__rst_ni;
    CData/*0:0*/ hwpe_stream_buffer_wrapper__DOT__clear_i;
    CData/*0:0*/ hwpe_stream_buffer_wrapper__DOT__test_mode_i;
    CData/*0:0*/ hwpe_stream_buffer_wrapper__DOT__push_i_valid;
    CData/*3:0*/ hwpe_stream_buffer_wrapper__DOT__push_i_strb;
    CData/*0:0*/ hwpe_stream_buffer_wrapper__DOT__push_i_ready;
    CData/*0:0*/ hwpe_stream_buffer_wrapper__DOT__pop_o_valid;
    CData/*3:0*/ hwpe_stream_buffer_wrapper__DOT__pop_o_strb;
    CData/*0:0*/ hwpe_stream_buffer_wrapper__DOT__pop_o_ready;
    CData/*0:0*/ hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__clk_i;
    CData/*0:0*/ hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__rst_ni;
    CData/*0:0*/ hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__clear_i;
    CData/*0:0*/ hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__test_mode_i;
    CData/*0:0*/ hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__i_cg__DOT__clk_i;
    CData/*0:0*/ hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__i_cg__DOT__en_i;
    CData/*0:0*/ hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__i_cg__DOT__test_en_i;
    CData/*0:0*/ hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__i_cg__DOT__clk_o;
    CData/*0:0*/ hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__i_cg__DOT__clk_en;
    CData/*0:0*/ __VstlFirstIteration;
    CData/*0:0*/ __VicoFirstIteration;
    CData/*0:0*/ __Vtrigprevexpr___TOP__hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__clk_gated__0;
    CData/*0:0*/ __Vtrigprevexpr___TOP__rst_ni__0;
    CData/*0:0*/ __VactContinue;
    VL_IN(push_i_data,31,0);
    VL_OUT(pop_o_data,31,0);
    IData/*31:0*/ hwpe_stream_buffer_wrapper__DOT__push_i_data;
    IData/*31:0*/ hwpe_stream_buffer_wrapper__DOT__pop_o_data;
    IData/*31:0*/ __VactIterCount;
    VlTriggerVec<1> __VstlTriggered;
    VlTriggerVec<1> __VicoTriggered;
    VlTriggerVec<2> __VactTriggered;
    VlTriggerVec<2> __VnbaTriggered;

    // INTERNAL VARIABLES
    Vtop__Syms* const vlSymsp;

    // PARAMETERS
    static constexpr CData/*0:0*/ hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__i_cg__DOT__IS_FUNCTIONAL = 1U;
    static constexpr IData/*31:0*/ hwpe_stream_buffer_wrapper__DOT__DATA_WIDTH = 0x00000020U;
    static constexpr IData/*31:0*/ hwpe_stream_buffer_wrapper__DOT__STRB_WIDTH = 4U;
    static constexpr IData/*31:0*/ hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__DATA_WIDTH = 0x00000020U;

    // CONSTRUCTORS
    Vtop___024root(Vtop__Syms* symsp, const char* v__name);
    ~Vtop___024root();
    VL_UNCOPYABLE(Vtop___024root);

    // INTERNAL METHODS
    void __Vconfigure(bool first);
};


#endif  // guard
