// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Symbol table implementation internals

#include "Vtop__pch.h"
#include "Vtop.h"
#include "Vtop___024root.h"
#include "Vtop___024unit.h"
#include "Vtop_hwpe_stream_intf_stream__D20.h"
#include "Vtop_hwpe_stream_package.h"

// FUNCTIONS
Vtop__Syms::~Vtop__Syms()
{

    // Tear down scope hierarchy
    __Vhier.remove(0, &__Vscope_hwpe_stream_buffer_wrapper);
    __Vhier.remove(0, &__Vscope_hwpe_stream_package);
    __Vhier.remove(&__Vscope_hwpe_stream_buffer_wrapper, &__Vscope_hwpe_stream_buffer_wrapper__i_hwpe_stream_buffer);
    __Vhier.remove(&__Vscope_hwpe_stream_buffer_wrapper, &__Vscope_hwpe_stream_buffer_wrapper__pop_intf_o);
    __Vhier.remove(&__Vscope_hwpe_stream_buffer_wrapper, &__Vscope_hwpe_stream_buffer_wrapper__push_intf_i);
    __Vhier.remove(&__Vscope_hwpe_stream_buffer_wrapper__i_hwpe_stream_buffer, &__Vscope_hwpe_stream_buffer_wrapper__i_hwpe_stream_buffer__i_cg);

}

Vtop__Syms::Vtop__Syms(VerilatedContext* contextp, const char* namep, Vtop* modelp)
    : VerilatedSyms{contextp}
    // Setup internal state of the Syms class
    , __Vm_modelp{modelp}
    // Setup module instances
    , TOP{this, namep}
    , TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o{this, Verilated::catName(namep, "hwpe_stream_buffer_wrapper.pop_intf_o")}
    , TOP__hwpe_stream_buffer_wrapper__DOT__push_intf_i{this, Verilated::catName(namep, "hwpe_stream_buffer_wrapper.push_intf_i")}
    , TOP__hwpe_stream_package{this, Verilated::catName(namep, "hwpe_stream_package")}
{
        // Check resources
        Verilated::stackCheck(29);
    // Configure time unit / time precision
    _vm_contextp__->timeunit(-12);
    _vm_contextp__->timeprecision(-12);
    // Setup each module's pointers to their submodules
    TOP.__PVT__hwpe_stream_buffer_wrapper__DOT__pop_intf_o = &TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o;
    TOP.__PVT__hwpe_stream_buffer_wrapper__DOT__push_intf_i = &TOP__hwpe_stream_buffer_wrapper__DOT__push_intf_i;
    TOP.__PVT__hwpe_stream_package = &TOP__hwpe_stream_package;
    // Setup each module's pointer back to symbol table (for public functions)
    TOP.__Vconfigure(true);
    TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.__Vconfigure(true);
    TOP__hwpe_stream_buffer_wrapper__DOT__push_intf_i.__Vconfigure(false);
    TOP__hwpe_stream_package.__Vconfigure(true);
    // Setup scopes
    __Vscope_TOP.configure(this, name(), "TOP", "TOP", "<null>", 0, VerilatedScope::SCOPE_OTHER);
    __Vscope_hwpe_stream_buffer_wrapper.configure(this, name(), "hwpe_stream_buffer_wrapper", "hwpe_stream_buffer_wrapper", "hwpe_stream_buffer_wrapper", -12, VerilatedScope::SCOPE_MODULE);
    __Vscope_hwpe_stream_buffer_wrapper__i_hwpe_stream_buffer.configure(this, name(), "hwpe_stream_buffer_wrapper.i_hwpe_stream_buffer", "i_hwpe_stream_buffer", "hwpe_stream_buffer", -12, VerilatedScope::SCOPE_MODULE);
    __Vscope_hwpe_stream_buffer_wrapper__i_hwpe_stream_buffer__i_cg.configure(this, name(), "hwpe_stream_buffer_wrapper.i_hwpe_stream_buffer.i_cg", "i_cg", "tc_clk_gating", -12, VerilatedScope::SCOPE_MODULE);
    __Vscope_hwpe_stream_buffer_wrapper__pop_intf_o.configure(this, name(), "hwpe_stream_buffer_wrapper.pop_intf_o", "pop_intf_o", "hwpe_stream_intf_stream", -12, VerilatedScope::SCOPE_MODULE);
    __Vscope_hwpe_stream_buffer_wrapper__push_intf_i.configure(this, name(), "hwpe_stream_buffer_wrapper.push_intf_i", "push_intf_i", "hwpe_stream_intf_stream", -12, VerilatedScope::SCOPE_MODULE);
    __Vscope_hwpe_stream_package.configure(this, name(), "hwpe_stream_package", "hwpe_stream_package", "hwpe_stream_package", -12, VerilatedScope::SCOPE_PACKAGE);

    // Set up scope hierarchy
    __Vhier.add(0, &__Vscope_hwpe_stream_buffer_wrapper);
    __Vhier.add(0, &__Vscope_hwpe_stream_package);
    __Vhier.add(&__Vscope_hwpe_stream_buffer_wrapper, &__Vscope_hwpe_stream_buffer_wrapper__i_hwpe_stream_buffer);
    __Vhier.add(&__Vscope_hwpe_stream_buffer_wrapper, &__Vscope_hwpe_stream_buffer_wrapper__pop_intf_o);
    __Vhier.add(&__Vscope_hwpe_stream_buffer_wrapper, &__Vscope_hwpe_stream_buffer_wrapper__push_intf_i);
    __Vhier.add(&__Vscope_hwpe_stream_buffer_wrapper__i_hwpe_stream_buffer, &__Vscope_hwpe_stream_buffer_wrapper__i_hwpe_stream_buffer__i_cg);

    // Setup export functions
    for (int __Vfinal = 0; __Vfinal < 2; ++__Vfinal) {
        __Vscope_TOP.varInsert(__Vfinal,"clear_i", &(TOP.clear_i), false, VLVT_UINT8,VLVD_IN|VLVF_PUB_RW,0,0);
        __Vscope_TOP.varInsert(__Vfinal,"clk_i", &(TOP.clk_i), false, VLVT_UINT8,VLVD_IN|VLVF_PUB_RW,0,0);
        __Vscope_TOP.varInsert(__Vfinal,"pop_o_data", &(TOP.pop_o_data), false, VLVT_UINT32,VLVD_OUT|VLVF_PUB_RW,0,1 ,31,0);
        __Vscope_TOP.varInsert(__Vfinal,"pop_o_ready", &(TOP.pop_o_ready), false, VLVT_UINT8,VLVD_IN|VLVF_PUB_RW,0,0);
        __Vscope_TOP.varInsert(__Vfinal,"pop_o_strb", &(TOP.pop_o_strb), false, VLVT_UINT8,VLVD_OUT|VLVF_PUB_RW,0,1 ,3,0);
        __Vscope_TOP.varInsert(__Vfinal,"pop_o_valid", &(TOP.pop_o_valid), false, VLVT_UINT8,VLVD_OUT|VLVF_PUB_RW,0,0);
        __Vscope_TOP.varInsert(__Vfinal,"push_i_data", &(TOP.push_i_data), false, VLVT_UINT32,VLVD_IN|VLVF_PUB_RW,0,1 ,31,0);
        __Vscope_TOP.varInsert(__Vfinal,"push_i_ready", &(TOP.push_i_ready), false, VLVT_UINT8,VLVD_OUT|VLVF_PUB_RW,0,0);
        __Vscope_TOP.varInsert(__Vfinal,"push_i_strb", &(TOP.push_i_strb), false, VLVT_UINT8,VLVD_IN|VLVF_PUB_RW,0,1 ,3,0);
        __Vscope_TOP.varInsert(__Vfinal,"push_i_valid", &(TOP.push_i_valid), false, VLVT_UINT8,VLVD_IN|VLVF_PUB_RW,0,0);
        __Vscope_TOP.varInsert(__Vfinal,"rst_ni", &(TOP.rst_ni), false, VLVT_UINT8,VLVD_IN|VLVF_PUB_RW,0,0);
        __Vscope_TOP.varInsert(__Vfinal,"test_mode_i", &(TOP.test_mode_i), false, VLVT_UINT8,VLVD_IN|VLVF_PUB_RW,0,0);
        __Vscope_hwpe_stream_buffer_wrapper.varInsert(__Vfinal,"DATA_WIDTH", const_cast<void*>(static_cast<const void*>(&(TOP.hwpe_stream_buffer_wrapper__DOT__DATA_WIDTH))), true, VLVT_UINT32,VLVD_NODIR|VLVF_PUB_RW|VLVF_DPI_CLAY,0,1 ,31,0);
        __Vscope_hwpe_stream_buffer_wrapper.varInsert(__Vfinal,"STRB_WIDTH", const_cast<void*>(static_cast<const void*>(&(TOP.hwpe_stream_buffer_wrapper__DOT__STRB_WIDTH))), true, VLVT_UINT32,VLVD_NODIR|VLVF_PUB_RW|VLVF_DPI_CLAY,0,1 ,31,0);
        __Vscope_hwpe_stream_buffer_wrapper.varInsert(__Vfinal,"clear_i", &(TOP.hwpe_stream_buffer_wrapper__DOT__clear_i), false, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW,0,0);
        __Vscope_hwpe_stream_buffer_wrapper.varInsert(__Vfinal,"clk_i", &(TOP.hwpe_stream_buffer_wrapper__DOT__clk_i), false, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW,0,0);
        __Vscope_hwpe_stream_buffer_wrapper.varInsert(__Vfinal,"pop_o_data", &(TOP.hwpe_stream_buffer_wrapper__DOT__pop_o_data), false, VLVT_UINT32,VLVD_NODIR|VLVF_PUB_RW,0,1 ,31,0);
        __Vscope_hwpe_stream_buffer_wrapper.varInsert(__Vfinal,"pop_o_ready", &(TOP.hwpe_stream_buffer_wrapper__DOT__pop_o_ready), false, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW,0,0);
        __Vscope_hwpe_stream_buffer_wrapper.varInsert(__Vfinal,"pop_o_strb", &(TOP.hwpe_stream_buffer_wrapper__DOT__pop_o_strb), false, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW,0,1 ,3,0);
        __Vscope_hwpe_stream_buffer_wrapper.varInsert(__Vfinal,"pop_o_valid", &(TOP.hwpe_stream_buffer_wrapper__DOT__pop_o_valid), false, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW,0,0);
        __Vscope_hwpe_stream_buffer_wrapper.varInsert(__Vfinal,"push_i_data", &(TOP.hwpe_stream_buffer_wrapper__DOT__push_i_data), false, VLVT_UINT32,VLVD_NODIR|VLVF_PUB_RW,0,1 ,31,0);
        __Vscope_hwpe_stream_buffer_wrapper.varInsert(__Vfinal,"push_i_ready", &(TOP.hwpe_stream_buffer_wrapper__DOT__push_i_ready), false, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW,0,0);
        __Vscope_hwpe_stream_buffer_wrapper.varInsert(__Vfinal,"push_i_strb", &(TOP.hwpe_stream_buffer_wrapper__DOT__push_i_strb), false, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW,0,1 ,3,0);
        __Vscope_hwpe_stream_buffer_wrapper.varInsert(__Vfinal,"push_i_valid", &(TOP.hwpe_stream_buffer_wrapper__DOT__push_i_valid), false, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW,0,0);
        __Vscope_hwpe_stream_buffer_wrapper.varInsert(__Vfinal,"rst_ni", &(TOP.hwpe_stream_buffer_wrapper__DOT__rst_ni), false, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW,0,0);
        __Vscope_hwpe_stream_buffer_wrapper.varInsert(__Vfinal,"test_mode_i", &(TOP.hwpe_stream_buffer_wrapper__DOT__test_mode_i), false, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW,0,0);
        __Vscope_hwpe_stream_buffer_wrapper__i_hwpe_stream_buffer.varInsert(__Vfinal,"DATA_WIDTH", const_cast<void*>(static_cast<const void*>(&(TOP.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__DATA_WIDTH))), true, VLVT_UINT32,VLVD_NODIR|VLVF_PUB_RW|VLVF_DPI_CLAY,0,1 ,31,0);
        __Vscope_hwpe_stream_buffer_wrapper__i_hwpe_stream_buffer.varInsert(__Vfinal,"clear_i", &(TOP.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__clear_i), false, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW,0,0);
        __Vscope_hwpe_stream_buffer_wrapper__i_hwpe_stream_buffer.varInsert(__Vfinal,"clk_gated", &(TOP.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__clk_gated), false, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW,0,0);
        __Vscope_hwpe_stream_buffer_wrapper__i_hwpe_stream_buffer.varInsert(__Vfinal,"clk_i", &(TOP.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__clk_i), false, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW,0,0);
        __Vscope_hwpe_stream_buffer_wrapper__i_hwpe_stream_buffer.varInsert(__Vfinal,"rst_ni", &(TOP.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__rst_ni), false, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW,0,0);
        __Vscope_hwpe_stream_buffer_wrapper__i_hwpe_stream_buffer.varInsert(__Vfinal,"test_mode_i", &(TOP.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__test_mode_i), false, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW,0,0);
        __Vscope_hwpe_stream_buffer_wrapper__i_hwpe_stream_buffer__i_cg.varInsert(__Vfinal,"IS_FUNCTIONAL", const_cast<void*>(static_cast<const void*>(&(TOP.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__i_cg__DOT__IS_FUNCTIONAL))), true, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW|VLVF_DPI_CLAY,0,0);
        __Vscope_hwpe_stream_buffer_wrapper__i_hwpe_stream_buffer__i_cg.varInsert(__Vfinal,"clk_en", &(TOP.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__i_cg__DOT__clk_en), false, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW,0,0);
        __Vscope_hwpe_stream_buffer_wrapper__i_hwpe_stream_buffer__i_cg.varInsert(__Vfinal,"clk_i", &(TOP.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__i_cg__DOT__clk_i), false, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW,0,0);
        __Vscope_hwpe_stream_buffer_wrapper__i_hwpe_stream_buffer__i_cg.varInsert(__Vfinal,"clk_o", &(TOP.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__i_cg__DOT__clk_o), false, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW,0,0);
        __Vscope_hwpe_stream_buffer_wrapper__i_hwpe_stream_buffer__i_cg.varInsert(__Vfinal,"en_i", &(TOP.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__i_cg__DOT__en_i), false, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW,0,0);
        __Vscope_hwpe_stream_buffer_wrapper__i_hwpe_stream_buffer__i_cg.varInsert(__Vfinal,"test_en_i", &(TOP.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__i_cg__DOT__test_en_i), false, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW,0,0);
        __Vscope_hwpe_stream_buffer_wrapper__pop_intf_o.varInsert(__Vfinal,"BYPASS_VCR_ASSERT", const_cast<void*>(static_cast<const void*>(&(TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.BYPASS_VCR_ASSERT))), true, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW|VLVF_DPI_CLAY,0,0);
        __Vscope_hwpe_stream_buffer_wrapper__pop_intf_o.varInsert(__Vfinal,"BYPASS_VDR_ASSERT", const_cast<void*>(static_cast<const void*>(&(TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.BYPASS_VDR_ASSERT))), true, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW|VLVF_DPI_CLAY,0,0);
        __Vscope_hwpe_stream_buffer_wrapper__pop_intf_o.varInsert(__Vfinal,"DATA_WIDTH", const_cast<void*>(static_cast<const void*>(&(TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.DATA_WIDTH))), true, VLVT_UINT32,VLVD_NODIR|VLVF_PUB_RW|VLVF_DPI_CLAY,0,1 ,31,0);
        __Vscope_hwpe_stream_buffer_wrapper__pop_intf_o.varInsert(__Vfinal,"STRB_WIDTH", const_cast<void*>(static_cast<const void*>(&(TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.STRB_WIDTH))), true, VLVT_UINT32,VLVD_NODIR|VLVF_PUB_RW|VLVF_DPI_CLAY,0,1 ,31,0);
        __Vscope_hwpe_stream_buffer_wrapper__pop_intf_o.varInsert(__Vfinal,"clk", &(TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.clk), false, VLVT_UINT8,VLVD_IN|VLVF_PUB_RW,0,0);
        __Vscope_hwpe_stream_buffer_wrapper__pop_intf_o.varInsert(__Vfinal,"data", &(TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.data), false, VLVT_UINT32,VLVD_NODIR|VLVF_PUB_RW,0,1 ,31,0);
        __Vscope_hwpe_stream_buffer_wrapper__pop_intf_o.varInsert(__Vfinal,"ready", &(TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.ready), false, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW,0,0);
        __Vscope_hwpe_stream_buffer_wrapper__pop_intf_o.varInsert(__Vfinal,"strb", &(TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.strb), false, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW,0,1 ,3,0);
        __Vscope_hwpe_stream_buffer_wrapper__pop_intf_o.varInsert(__Vfinal,"valid", &(TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.valid), false, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW,0,0);
        __Vscope_hwpe_stream_buffer_wrapper__push_intf_i.varInsert(__Vfinal,"BYPASS_VCR_ASSERT", const_cast<void*>(static_cast<const void*>(&(TOP__hwpe_stream_buffer_wrapper__DOT__push_intf_i.BYPASS_VCR_ASSERT))), true, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW|VLVF_DPI_CLAY,0,0);
        __Vscope_hwpe_stream_buffer_wrapper__push_intf_i.varInsert(__Vfinal,"BYPASS_VDR_ASSERT", const_cast<void*>(static_cast<const void*>(&(TOP__hwpe_stream_buffer_wrapper__DOT__push_intf_i.BYPASS_VDR_ASSERT))), true, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW|VLVF_DPI_CLAY,0,0);
        __Vscope_hwpe_stream_buffer_wrapper__push_intf_i.varInsert(__Vfinal,"DATA_WIDTH", const_cast<void*>(static_cast<const void*>(&(TOP__hwpe_stream_buffer_wrapper__DOT__push_intf_i.DATA_WIDTH))), true, VLVT_UINT32,VLVD_NODIR|VLVF_PUB_RW|VLVF_DPI_CLAY,0,1 ,31,0);
        __Vscope_hwpe_stream_buffer_wrapper__push_intf_i.varInsert(__Vfinal,"STRB_WIDTH", const_cast<void*>(static_cast<const void*>(&(TOP__hwpe_stream_buffer_wrapper__DOT__push_intf_i.STRB_WIDTH))), true, VLVT_UINT32,VLVD_NODIR|VLVF_PUB_RW|VLVF_DPI_CLAY,0,1 ,31,0);
        __Vscope_hwpe_stream_buffer_wrapper__push_intf_i.varInsert(__Vfinal,"clk", &(TOP__hwpe_stream_buffer_wrapper__DOT__push_intf_i.clk), false, VLVT_UINT8,VLVD_IN|VLVF_PUB_RW,0,0);
        __Vscope_hwpe_stream_buffer_wrapper__push_intf_i.varInsert(__Vfinal,"data", &(TOP__hwpe_stream_buffer_wrapper__DOT__push_intf_i.data), false, VLVT_UINT32,VLVD_NODIR|VLVF_PUB_RW,0,1 ,31,0);
        __Vscope_hwpe_stream_buffer_wrapper__push_intf_i.varInsert(__Vfinal,"ready", &(TOP__hwpe_stream_buffer_wrapper__DOT__push_intf_i.ready), false, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW,0,0);
        __Vscope_hwpe_stream_buffer_wrapper__push_intf_i.varInsert(__Vfinal,"strb", &(TOP__hwpe_stream_buffer_wrapper__DOT__push_intf_i.strb), false, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW,0,1 ,3,0);
        __Vscope_hwpe_stream_buffer_wrapper__push_intf_i.varInsert(__Vfinal,"valid", &(TOP__hwpe_stream_buffer_wrapper__DOT__push_intf_i.valid), false, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW,0,0);
        __Vscope_hwpe_stream_package.varInsert(__Vfinal,"HWPE_STREAM_ADDRESSGEN_1D", const_cast<void*>(static_cast<const void*>(&(TOP__hwpe_stream_package.HWPE_STREAM_ADDRESSGEN_1D))), true, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW,0,1 ,1,0);
        __Vscope_hwpe_stream_package.varInsert(__Vfinal,"HWPE_STREAM_ADDRESSGEN_2D", const_cast<void*>(static_cast<const void*>(&(TOP__hwpe_stream_package.HWPE_STREAM_ADDRESSGEN_2D))), true, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW,0,1 ,1,0);
        __Vscope_hwpe_stream_package.varInsert(__Vfinal,"HWPE_STREAM_ADDRESSGEN_3D", const_cast<void*>(static_cast<const void*>(&(TOP__hwpe_stream_package.HWPE_STREAM_ADDRESSGEN_3D))), true, VLVT_UINT8,VLVD_NODIR|VLVF_PUB_RW,0,1 ,1,0);
        __Vscope_hwpe_stream_package.varInsert(__Vfinal,"HWPE_STREAM_REALIGN_SINK", const_cast<void*>(static_cast<const void*>(&(TOP__hwpe_stream_package.HWPE_STREAM_REALIGN_SINK))), true, VLVT_UINT32,VLVD_NODIR|VLVF_PUB_RW|VLVF_DPI_CLAY,0,1 ,31,0);
        __Vscope_hwpe_stream_package.varInsert(__Vfinal,"HWPE_STREAM_REALIGN_SOURCE", const_cast<void*>(static_cast<const void*>(&(TOP__hwpe_stream_package.HWPE_STREAM_REALIGN_SOURCE))), true, VLVT_UINT32,VLVD_NODIR|VLVF_PUB_RW|VLVF_DPI_CLAY,0,1 ,31,0);
        __Vscope_hwpe_stream_package.varInsert(__Vfinal,"NB_SERDES_STREAMS_MAX", const_cast<void*>(static_cast<const void*>(&(TOP__hwpe_stream_package.NB_SERDES_STREAMS_MAX))), true, VLVT_UINT32,VLVD_NODIR|VLVF_PUB_RW|VLVF_DPI_CLAY,0,1 ,31,0);
    }
}
