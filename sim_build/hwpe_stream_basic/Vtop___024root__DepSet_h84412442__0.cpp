// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Design implementation internals
// See Vtop.h for the primary calling header

#include "Vtop__pch.h"
#include "Vtop__Syms.h"
#include "Vtop___024root.h"

#ifdef VL_DEBUG
VL_ATTR_COLD void Vtop___024root___dump_triggers__ico(Vtop___024root* vlSelf);
#endif  // VL_DEBUG

void Vtop___024root___eval_triggers__ico(Vtop___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vtop___024root___eval_triggers__ico\n"); );
    Vtop__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    vlSelfRef.__VicoTriggered.setBit(0U, (IData)(vlSelfRef.__VicoFirstIteration));
#ifdef VL_DEBUG
    if (VL_UNLIKELY(vlSymsp->_vm_contextp__->debug())) {
        Vtop___024root___dump_triggers__ico(vlSelf);
    }
#endif
}

VL_INLINE_OPT void Vtop___024root___ico_sequent__TOP__0(Vtop___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vtop___024root___ico_sequent__TOP__0\n"); );
    Vtop__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Init
    CData/*0:0*/ hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT____Vcellinp__i_cg__en_i;
    hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT____Vcellinp__i_cg__en_i = 0;
    // Body
    vlSelfRef.hwpe_stream_buffer_wrapper__DOT__push_i_valid 
        = vlSelfRef.push_i_valid;
    vlSelfRef.hwpe_stream_buffer_wrapper__DOT__push_i_data 
        = vlSelfRef.push_i_data;
    vlSelfRef.hwpe_stream_buffer_wrapper__DOT__push_i_strb 
        = vlSelfRef.push_i_strb;
    vlSelfRef.hwpe_stream_buffer_wrapper__DOT__pop_o_ready 
        = vlSelfRef.pop_o_ready;
    vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__push_intf_i.clk 
        = vlSelfRef.clk_i;
    vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.clk 
        = vlSelfRef.clk_i;
    vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__push_intf_i.valid 
        = vlSelfRef.push_i_valid;
    vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__push_intf_i.data 
        = vlSelfRef.push_i_data;
    vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__push_intf_i.strb 
        = vlSelfRef.push_i_strb;
    vlSelfRef.hwpe_stream_buffer_wrapper__DOT__rst_ni 
        = vlSelfRef.rst_ni;
    vlSelfRef.hwpe_stream_buffer_wrapper__DOT__clear_i 
        = vlSelfRef.clear_i;
    vlSelfRef.hwpe_stream_buffer_wrapper__DOT__pop_o_valid 
        = vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.valid;
    vlSelfRef.hwpe_stream_buffer_wrapper__DOT__pop_o_data 
        = vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.data;
    vlSelfRef.hwpe_stream_buffer_wrapper__DOT__pop_o_strb 
        = vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.strb;
    vlSelfRef.hwpe_stream_buffer_wrapper__DOT__clk_i 
        = vlSelfRef.clk_i;
    vlSelfRef.hwpe_stream_buffer_wrapper__DOT__test_mode_i 
        = vlSelfRef.test_mode_i;
    vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.ready 
        = vlSelfRef.pop_o_ready;
    vlSelfRef.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__rst_ni 
        = vlSelfRef.hwpe_stream_buffer_wrapper__DOT__rst_ni;
    vlSelfRef.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__clear_i 
        = vlSelfRef.hwpe_stream_buffer_wrapper__DOT__clear_i;
    vlSelfRef.pop_o_valid = vlSelfRef.hwpe_stream_buffer_wrapper__DOT__pop_o_valid;
    vlSelfRef.pop_o_data = vlSelfRef.hwpe_stream_buffer_wrapper__DOT__pop_o_data;
    vlSelfRef.pop_o_strb = vlSelfRef.hwpe_stream_buffer_wrapper__DOT__pop_o_strb;
    vlSelfRef.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__clk_i 
        = vlSelfRef.hwpe_stream_buffer_wrapper__DOT__clk_i;
    vlSelfRef.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__test_mode_i 
        = vlSelfRef.hwpe_stream_buffer_wrapper__DOT__test_mode_i;
    vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__push_intf_i.ready 
        = vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.ready;
    hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT____Vcellinp__i_cg__en_i 
        = ((IData)(vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.ready) 
           | (IData)(vlSelfRef.clear_i));
    vlSelfRef.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__i_cg__DOT__clk_i 
        = vlSelfRef.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__clk_i;
    vlSelfRef.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__i_cg__DOT__test_en_i 
        = vlSelfRef.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__test_mode_i;
    vlSelfRef.hwpe_stream_buffer_wrapper__DOT__push_i_ready 
        = vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__push_intf_i.ready;
    vlSelfRef.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__i_cg__DOT__en_i 
        = hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT____Vcellinp__i_cg__en_i;
    if ((1U & (~ (IData)(vlSelfRef.clk_i)))) {
        vlSelfRef.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__i_cg__DOT__clk_en 
            = ((IData)(hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT____Vcellinp__i_cg__en_i) 
               | (IData)(vlSelfRef.test_mode_i));
    }
    vlSelfRef.push_i_ready = vlSelfRef.hwpe_stream_buffer_wrapper__DOT__push_i_ready;
    vlSelfRef.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__clk_gated 
        = ((IData)(vlSelfRef.clk_i) & (IData)(vlSelfRef.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__i_cg__DOT__clk_en));
    vlSelfRef.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__i_cg__DOT__clk_o 
        = vlSelfRef.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__clk_gated;
}

#ifdef VL_DEBUG
VL_ATTR_COLD void Vtop___024root___dump_triggers__act(Vtop___024root* vlSelf);
#endif  // VL_DEBUG

void Vtop___024root___eval_triggers__act(Vtop___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vtop___024root___eval_triggers__act\n"); );
    Vtop__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    vlSelfRef.__VactTriggered.setBit(0U, ((IData)(vlSelfRef.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__clk_gated) 
                                          & (~ (IData)(vlSelfRef.__Vtrigprevexpr___TOP__hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__clk_gated__0))));
    vlSelfRef.__VactTriggered.setBit(1U, ((~ (IData)(vlSelfRef.rst_ni)) 
                                          & (IData)(vlSelfRef.__Vtrigprevexpr___TOP__rst_ni__0)));
    vlSelfRef.__Vtrigprevexpr___TOP__hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__clk_gated__0 
        = vlSelfRef.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__clk_gated;
    vlSelfRef.__Vtrigprevexpr___TOP__rst_ni__0 = vlSelfRef.rst_ni;
#ifdef VL_DEBUG
    if (VL_UNLIKELY(vlSymsp->_vm_contextp__->debug())) {
        Vtop___024root___dump_triggers__act(vlSelf);
    }
#endif
}

VL_INLINE_OPT void Vtop___024root___nba_sequent__TOP__0(Vtop___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vtop___024root___nba_sequent__TOP__0\n"); );
    Vtop__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    if (vlSelfRef.rst_ni) {
        if (vlSelfRef.clear_i) {
            vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.data = 0U;
            vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.strb = 0U;
        } else {
            vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.data 
                = vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__push_intf_i.data;
            vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.strb 
                = vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__push_intf_i.strb;
        }
    } else {
        vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.data = 0U;
        vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.strb = 0U;
    }
    vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.valid 
        = ((IData)(vlSelfRef.rst_ni) && ((1U & (~ (IData)(vlSelfRef.clear_i))) 
                                         && (IData)(vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__push_intf_i.valid)));
}

VL_INLINE_OPT void Vtop___024root___nba_sequent__TOP__1(Vtop___024root* vlSelf) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vtop___024root___nba_sequent__TOP__1\n"); );
    Vtop__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Body
    vlSelfRef.hwpe_stream_buffer_wrapper__DOT__pop_o_data 
        = vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.data;
    vlSelfRef.hwpe_stream_buffer_wrapper__DOT__pop_o_strb 
        = vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.strb;
    vlSelfRef.hwpe_stream_buffer_wrapper__DOT__pop_o_valid 
        = vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.valid;
    vlSelfRef.pop_o_data = vlSelfRef.hwpe_stream_buffer_wrapper__DOT__pop_o_data;
    vlSelfRef.pop_o_strb = vlSelfRef.hwpe_stream_buffer_wrapper__DOT__pop_o_strb;
    vlSelfRef.pop_o_valid = vlSelfRef.hwpe_stream_buffer_wrapper__DOT__pop_o_valid;
}
