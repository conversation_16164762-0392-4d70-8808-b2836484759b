$version Generated by VerilatedVcd $end
$timescale 1ps $end
 $scope module $rootio $end
 $upscope $end
 $scope module hwpe_stream_package $end
  $var wire 32 S HWPE_STREAM_REALIGN_SOURCE [31:0] $end
  $var wire 32 T HWPE_STREAM_REALIGN_SINK [31:0] $end
  $var wire 2 U HWPE_STREAM_ADDRESSGEN_3D [1:0] $end
  $var wire 2 V HWPE_STREAM_ADDRESSGEN_2D [1:0] $end
  $var wire 2 W HWPE_STREAM_ADDRESSGEN_1D [1:0] $end
  $var wire 32 X NB_SERDES_STREAMS_MAX [31:0] $end
 $upscope $end
 $var wire 1 # clk_i $end
 $var wire 1 $ rst_ni $end
 $var wire 1 % clear_i $end
 $var wire 1 & test_mode_i $end
 $var wire 1 ' push_i_valid $end
 $var wire 32 ( push_i_data [31:0] $end
 $var wire 4 ) push_i_strb [3:0] $end
 $var wire 1 * push_i_ready $end
 $var wire 1 + pop_o_valid $end
 $var wire 32 , pop_o_data [31:0] $end
 $var wire 4 - pop_o_strb [3:0] $end
 $var wire 1 . pop_o_ready $end
 $scope module hwpe_stream_buffer_wrapper $end
  $var wire 32 O DATA_WIDTH [31:0] $end
  $var wire 32 P STRB_WIDTH [31:0] $end
  $var wire 1 / clk_i $end
  $var wire 1 0 rst_ni $end
  $var wire 1 1 clear_i $end
  $var wire 1 2 test_mode_i $end
  $var wire 1 3 push_i_valid $end
  $var wire 32 4 push_i_data [31:0] $end
  $var wire 4 5 push_i_strb [3:0] $end
  $var wire 1 6 push_i_ready $end
  $var wire 1 7 pop_o_valid $end
  $var wire 32 8 pop_o_data [31:0] $end
  $var wire 4 9 pop_o_strb [3:0] $end
  $var wire 1 : pop_o_ready $end
  $scope module push_intf_i $end
   $var wire 1 E clk $end
   $var wire 32 O DATA_WIDTH [31:0] $end
   $var wire 32 P STRB_WIDTH [31:0] $end
   $var wire 1 R BYPASS_VCR_ASSERT $end
   $var wire 1 R BYPASS_VDR_ASSERT $end
   $var wire 1 F valid $end
   $var wire 1 G ready $end
   $var wire 32 H data [31:0] $end
   $var wire 4 I strb [3:0] $end
  $upscope $end
  $scope module pop_intf_o $end
   $var wire 1 J clk $end
   $var wire 32 O DATA_WIDTH [31:0] $end
   $var wire 32 P STRB_WIDTH [31:0] $end
   $var wire 1 R BYPASS_VCR_ASSERT $end
   $var wire 1 R BYPASS_VDR_ASSERT $end
   $var wire 1 K valid $end
   $var wire 1 L ready $end
   $var wire 32 M data [31:0] $end
   $var wire 4 N strb [3:0] $end
  $upscope $end
  $scope module i_hwpe_stream_buffer $end
   $var wire 32 O DATA_WIDTH [31:0] $end
   $var wire 1 ; clk_i $end
   $var wire 1 < rst_ni $end
   $var wire 1 = clear_i $end
   $var wire 1 > test_mode_i $end
   $scope module push_i $end
    $var wire 1 E clk $end
    $var wire 32 O DATA_WIDTH [31:0] $end
    $var wire 32 P STRB_WIDTH [31:0] $end
    $var wire 1 R BYPASS_VCR_ASSERT $end
    $var wire 1 R BYPASS_VDR_ASSERT $end
    $var wire 1 F valid $end
    $var wire 1 G ready $end
    $var wire 32 H data [31:0] $end
    $var wire 4 I strb [3:0] $end
   $upscope $end
   $scope module pop_o $end
    $var wire 1 J clk $end
    $var wire 32 O DATA_WIDTH [31:0] $end
    $var wire 32 P STRB_WIDTH [31:0] $end
    $var wire 1 R BYPASS_VCR_ASSERT $end
    $var wire 1 R BYPASS_VDR_ASSERT $end
    $var wire 1 K valid $end
    $var wire 1 L ready $end
    $var wire 32 M data [31:0] $end
    $var wire 4 N strb [3:0] $end
   $upscope $end
   $var wire 1 ? clk_gated $end
   $scope module i_cg $end
    $var wire 1 Q IS_FUNCTIONAL $end
    $var wire 1 @ clk_i $end
    $var wire 1 A en_i $end
    $var wire 1 B test_en_i $end
    $var wire 1 C clk_o $end
    $var wire 1 D clk_en $end
   $upscope $end
  $upscope $end
 $upscope $end
$enddefinitions $end


#0
1#
0$
0%
0&
0'
b00000000000000000000000000000000 (
b0000 )
0*
0+
b00000000000000000000000000000000 ,
b0000 -
0.
1/
00
01
02
03
b00000000000000000000000000000000 4
b0000 5
06
07
b00000000000000000000000000000000 8
b0000 9
0:
1;
0<
0=
0>
0?
1@
0A
0B
0C
0D
1E
0F
0G
b00000000000000000000000000000000 H
b0000 I
1J
0K
0L
b00000000000000000000000000000000 M
b0000 N
b00000000000000000000000000100000 O
b00000000000000000000000000000100 P
1Q
0R
b00000000000000000000000000000000 S
b00000000000000000000000000000001 T
b11 U
b01 V
b00 W
b00000000000000000000010000000000 X
#5000
0#
0/
0;
0@
0E
0J
#10000
1#
1/
1;
1@
1E
1J
#15000
0#
0/
0;
0@
0E
0J
#20000
1#
1/
1;
1@
1E
1J
#25000
0#
0/
0;
0@
0E
0J
#30000
1#
1/
1;
1@
1E
1J
#35000
0#
0/
0;
0@
0E
0J
#40000
1#
1$
1*
1.
1/
10
16
1:
1;
1<
1@
1A
1E
1G
1J
1L
#45000
0#
0/
0;
0@
1D
0E
0J
#50000
1#
1/
1;
1?
1@
1C
1E
1J
#55000
0#
0/
0;
0?
0@
0C
0E
0J
#60000
1#
1/
1;
1?
1@
1C
1E
1J
#65000
0#
0/
0;
0?
0@
0C
0E
0J
#70000
1#
1/
1;
1?
1@
1C
1E
1J
#75000
0#
0/
0;
0?
0@
0C
0E
0J
#80000
1#
1/
1;
1?
1@
1C
1E
1J
#85000
0#
0/
0;
0?
0@
0C
0E
0J
#90000
1#
1/
1;
1?
1@
1C
1E
1J
#95000
0#
0/
0;
0?
0@
0C
0E
0J
#100000
1#
1'
b00000000000000000000000100000000 (
b1111 )
1/
13
b00000000000000000000000100000000 4
b1111 5
1;
1?
1@
1C
1E
1F
b00000000000000000000000100000000 H
b1111 I
1J
#105000
0#
0/
0;
0?
0@
0C
0E
0J
#110000
1#
1+
b00000000000000000000000100000000 ,
b1111 -
1/
17
b00000000000000000000000100000000 8
b1111 9
1;
1?
1@
1C
1E
1J
1K
b00000000000000000000000100000000 M
b1111 N
#110001
0$
0+
b00000000000000000000000000000000 ,
b0000 -
00
07
b00000000000000000000000000000000 8
b0000 9
0<
0K
b00000000000000000000000000000000 M
b0000 N
#115001
0#
0/
0;
0?
0@
0C
0E
0J
#120001
1#
1/
1;
1?
1@
1C
1E
1J
#125001
0#
0/
0;
0?
0@
0C
0E
0J
#130001
1#
1/
1;
1?
1@
1C
1E
1J
#135001
0#
0/
0;
0?
0@
0C
0E
0J
#140001
1#
1/
1;
1?
1@
1C
1E
1J
#145001
0#
0/
0;
0?
0@
0C
0E
0J
#150001
1#
1/
1;
1?
1@
1C
1E
1J
#155001
0#
0/
0;
0?
0@
0C
0E
0J
#160001
1#
1$
0'
b00000000000000000000000000000000 (
b0000 )
1/
10
03
b00000000000000000000000000000000 4
b0000 5
1;
1<
1?
1@
1C
1E
0F
b00000000000000000000000000000000 H
b0000 I
1J
#165001
0#
0/
0;
0?
0@
0C
0E
0J
#170001
1#
1/
1;
1?
1@
1C
1E
1J
#175001
0#
0/
0;
0?
0@
0C
0E
0J
#180001
1#
1/
1;
1?
1@
1C
1E
1J
#185001
0#
0/
0;
0?
0@
0C
0E
0J
#190001
1#
1/
1;
1?
1@
1C
1E
1J
#195001
0#
0/
0;
0?
0@
0C
0E
0J
#200001
1#
1/
1;
1?
1@
1C
1E
1J
#205001
0#
0/
0;
0?
0@
0C
0E
0J
#210001
1#
1/
1;
1?
1@
1C
1E
1J
#215001
0#
0/
0;
0?
0@
0C
0E
0J
#220001
1#
1'
b10111101011100010011010011101100 (
b1111 )
1/
13
b10111101011100010011010011101100 4
b1111 5
1;
1?
1@
1C
1E
1F
b10111101011100010011010011101100 H
b1111 I
1J
#225001
0#
0/
0;
0?
0@
0C
0E
0J
#230001
1#
1+
b10111101011100010011010011101100 ,
b1111 -
1/
17
b10111101011100010011010011101100 8
b1111 9
1;
1?
1@
1C
1E
1J
1K
b10111101011100010011010011101100 M
b1111 N
#230002
0$
0+
b00000000000000000000000000000000 ,
b0000 -
00
07
b00000000000000000000000000000000 8
b0000 9
0<
0K
b00000000000000000000000000000000 M
b0000 N
#235002
0#
0/
0;
0?
0@
0C
0E
0J
#240002
1#
1/
1;
1?
1@
1C
1E
1J
#245002
0#
0/
0;
0?
0@
0C
0E
0J
#250002
1#
1/
1;
1?
1@
1C
1E
1J
#255002
0#
0/
0;
0?
0@
0C
0E
0J
#260002
1#
1/
1;
1?
1@
1C
1E
1J
#265002
0#
0/
0;
0?
0@
0C
0E
0J
#270002
1#
1/
1;
1?
1@
1C
1E
1J
#275002
0#
0/
0;
0?
0@
0C
0E
0J
#280002
1#
1$
0'
b00000000000000000000000000000000 (
b0000 )
1/
10
03
b00000000000000000000000000000000 4
b0000 5
1;
1<
1?
1@
1C
1E
0F
b00000000000000000000000000000000 H
b0000 I
1J
#285002
0#
0/
0;
0?
0@
0C
0E
0J
#290002
1#
1/
1;
1?
1@
1C
1E
1J
#295002
0#
0/
0;
0?
0@
0C
0E
0J
#300002
1#
1/
1;
1?
1@
1C
1E
1J
#305002
0#
0/
0;
0?
0@
0C
0E
0J
#310002
1#
1/
1;
1?
1@
1C
1E
1J
#315002
0#
0/
0;
0?
0@
0C
0E
0J
#320002
1#
1/
1;
1?
1@
1C
1E
1J
#325002
0#
0/
0;
0?
0@
0C
0E
0J
#330002
1#
1/
1;
1?
1@
1C
1E
1J
#335002
0#
0/
0;
0?
0@
0C
0E
0J
#340002
1#
1'
b11000111011000010100000001011001 (
b1111 )
1/
13
b11000111011000010100000001011001 4
b1111 5
1;
1?
1@
1C
1E
1F
b11000111011000010100000001011001 H
b1111 I
1J
#345002
0#
0/
0;
0?
0@
0C
0E
0J
#350002
1#
1+
b11000111011000010100000001011001 ,
b1111 -
1/
17
b11000111011000010100000001011001 8
b1111 9
1;
1?
1@
1C
1E
1J
1K
b11000111011000010100000001011001 M
b1111 N
#350003
0$
0+
b00000000000000000000000000000000 ,
b0000 -
00
07
b00000000000000000000000000000000 8
b0000 9
0<
0K
b00000000000000000000000000000000 M
b0000 N
#355003
0#
0/
0;
0?
0@
0C
0E
0J
#360003
1#
1/
1;
1?
1@
1C
1E
1J
#365003
0#
0/
0;
0?
0@
0C
0E
0J
#370003
1#
1/
1;
1?
1@
1C
1E
1J
#375003
0#
0/
0;
0?
0@
0C
0E
0J
#380003
1#
1/
1;
1?
1@
1C
1E
1J
#385003
0#
0/
0;
0?
0@
0C
0E
0J
#390003
1#
1/
1;
1?
1@
1C
1E
1J
#395003
0#
0/
0;
0?
0@
0C
0E
0J
#400003
1#
1$
0'
b00000000000000000000000000000000 (
b0000 )
1/
10
03
b00000000000000000000000000000000 4
b0000 5
1;
1<
1?
1@
1C
1E
0F
b00000000000000000000000000000000 H
b0000 I
1J
#405003
0#
0/
0;
0?
0@
0C
0E
0J
#410003
1#
1/
1;
1?
1@
1C
1E
1J
#415003
0#
0/
0;
0?
0@
0C
0E
0J
#420003
1#
1/
1;
1?
1@
1C
1E
1J
#425003
0#
0/
0;
0?
0@
0C
0E
0J
#430003
1#
1/
1;
1?
1@
1C
1E
1J
#435003
0#
0/
0;
0?
0@
0C
0E
0J
#440003
1#
1/
1;
1?
1@
1C
1E
1J
#445003
0#
0/
0;
0?
0@
0C
0E
0J
#450003
1#
1/
1;
1?
1@
1C
1E
1J
#455003
0#
0/
0;
0?
0@
0C
0E
0J
#460003
1#
1'
b00010010001101000101011001111000 (
b1111 )
1/
13
b00010010001101000101011001111000 4
b1111 5
1;
1?
1@
1C
1E
1F
b00010010001101000101011001111000 H
b1111 I
1J
#465003
0#
0/
0;
0?
0@
0C
0E
0J
#470003
1#
1+
b00010010001101000101011001111000 ,
b1111 -
1/
17
b00010010001101000101011001111000 8
b1111 9
1;
1?
1@
1C
1E
1J
1K
b00010010001101000101011001111000 M
b1111 N
#470004
