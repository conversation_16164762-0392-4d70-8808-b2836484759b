# DESCRIPTION: Verilator output: Timestamp data for --skip-identical.  Delete at will.
C "-cc --exe -Mdir /home/<USER>/asic_learn/sim_build/hwpe_stream_basic -DCOCOTB_SIM=1 --top-module hwpe_stream_buffer_wrapper --vpi --public-flat-rw --prefix Vtop -o hwpe_stream_buffer_wrapper -LDFLAGS -Wl,-rpath,/home/<USER>/venv/eda_tool/lib/python3.12/site-packages/cocotb/libs -L/home/<USER>/venv/eda_tool/lib/python3.12/site-packages/cocotb/libs -lcocotbvpi_verilator --trace /home/<USER>/venv/eda_tool/lib/python3.12/site-packages/cocotb/share/lib/verilator/verilator.cpp /home/<USER>/asic_learn/hwpe-stream/rtl/hwpe_stream_package.sv /home/<USER>/asic_learn/hwpe-stream/rtl/hwpe_stream_interfaces.sv /home/<USER>/asic_learn/hwpe-stream/rtl/basic/hwpe_stream_buffer.sv /home/<USER>/asic_learn/tech_cells_generic/src/rtl/tc_clk.sv /home/<USER>/asic_learn/hwpe-stream/verification/hwpe_stream_buffer_wrapper.sv"
S      2143   139022  1750250968   422981025  1750250968   422981025 "/home/<USER>/asic_learn/hwpe-stream/rtl/basic/hwpe_stream_buffer.sv"
S      4159   139039  1748350358   880525196  1748350358   880525196 "/home/<USER>/asic_learn/hwpe-stream/rtl/hwpe_stream_interfaces.sv"
S      4047   139040  1750250140   211170876  1750250140   211170876 "/home/<USER>/asic_learn/hwpe-stream/rtl/hwpe_stream_package.sv"
S      2037   194555  1750254536   710210651  1750254536   710210651 "/home/<USER>/asic_learn/hwpe-stream/verification/hwpe_stream_buffer_wrapper.sv"
T      5766   194579  1750255290   462043319  1750255290   462043319 "/home/<USER>/asic_learn/sim_build/hwpe_stream_basic/Vtop.cpp"
T      4197   194578  1750255290   462043319  1750255290   462043319 "/home/<USER>/asic_learn/sim_build/hwpe_stream_basic/Vtop.h"
T      2328   194601  1750255290   466043318  1750255290   466043318 "/home/<USER>/asic_learn/sim_build/hwpe_stream_basic/Vtop.mk"
T       669   194577  1750255290   462043319  1750255290   462043319 "/home/<USER>/asic_learn/sim_build/hwpe_stream_basic/Vtop__Dpi.cpp"
T       520   194576  1750255290   462043319  1750255290   462043319 "/home/<USER>/asic_learn/sim_build/hwpe_stream_basic/Vtop__Dpi.h"
T     16795   194574  1750255290   462043319  1750255290   462043319 "/home/<USER>/asic_learn/sim_build/hwpe_stream_basic/Vtop__Syms.cpp"
T      2010   194575  1750255290   462043319  1750255290   462043319 "/home/<USER>/asic_learn/sim_build/hwpe_stream_basic/Vtop__Syms.h"
T       290   194598  1750255290   466043318  1750255290   466043318 "/home/<USER>/asic_learn/sim_build/hwpe_stream_basic/Vtop__TraceDecls__0__Slow.cpp"
T      5356   194599  1750255290   466043318  1750255290   466043318 "/home/<USER>/asic_learn/sim_build/hwpe_stream_basic/Vtop__Trace__0.cpp"
T     20368   194597  1750255290   466043318  1750255290   466043318 "/home/<USER>/asic_learn/sim_build/hwpe_stream_basic/Vtop__Trace__0__Slow.cpp"
T      3879   194581  1750255290   462043319  1750255290   462043319 "/home/<USER>/asic_learn/sim_build/hwpe_stream_basic/Vtop___024root.h"
T      9262   194588  1750255290   462043319  1750255290   462043319 "/home/<USER>/asic_learn/sim_build/hwpe_stream_basic/Vtop___024root__DepSet_h84412442__0.cpp"
T       845   194586  1750255290   462043319  1750255290   462043319 "/home/<USER>/asic_learn/sim_build/hwpe_stream_basic/Vtop___024root__DepSet_h84412442__0__Slow.cpp"
T      7119   194589  1750255290   462043319  1750255290   462043319 "/home/<USER>/asic_learn/sim_build/hwpe_stream_basic/Vtop___024root__DepSet_heccd7ead__0.cpp"
T      9439   194587  1750255290   462043319  1750255290   462043319 "/home/<USER>/asic_learn/sim_build/hwpe_stream_basic/Vtop___024root__DepSet_heccd7ead__0__Slow.cpp"
T      1073   194585  1750255290   462043319  1750255290   462043319 "/home/<USER>/asic_learn/sim_build/hwpe_stream_basic/Vtop___024root__Slow.cpp"
T       627   194582  1750255290   462043319  1750255290   462043319 "/home/<USER>/asic_learn/sim_build/hwpe_stream_basic/Vtop___024unit.h"
T       467   194591  1750255290   462043319  1750255290   462043319 "/home/<USER>/asic_learn/sim_build/hwpe_stream_basic/Vtop___024unit__DepSet_hff17caec__0__Slow.cpp"
T       620   194590  1750255290   462043319  1750255290   462043319 "/home/<USER>/asic_learn/sim_build/hwpe_stream_basic/Vtop___024unit__Slow.cpp"
T       773   194580  1750255290   462043319  1750255290   462043319 "/home/<USER>/asic_learn/sim_build/hwpe_stream_basic/Vtop__pch.h"
T      2801   194605  1750255290   466043318  1750255290   466043318 "/home/<USER>/asic_learn/sim_build/hwpe_stream_basic/Vtop__ver.d"
T         0        0  1750255290   466043318  1750255290   466043318 "/home/<USER>/asic_learn/sim_build/hwpe_stream_basic/Vtop__verFiles.dat"
T      2183   194600  1750255290   466043318  1750255290   466043318 "/home/<USER>/asic_learn/sim_build/hwpe_stream_basic/Vtop_classes.mk"
T      1207   194583  1750255290   462043319  1750255290   462043319 "/home/<USER>/asic_learn/sim_build/hwpe_stream_basic/Vtop_hwpe_stream_intf_stream__D20.h"
T       432   194594  1750255290   462043319  1750255290   462043319 "/home/<USER>/asic_learn/sim_build/hwpe_stream_basic/Vtop_hwpe_stream_intf_stream__D20__DepSet_h3dd2c6a9__0.cpp"
T       754   194593  1750255290   462043319  1750255290   462043319 "/home/<USER>/asic_learn/sim_build/hwpe_stream_basic/Vtop_hwpe_stream_intf_stream__D20__DepSet_h3dd2c6a9__0__Slow.cpp"
T      1152   194592  1750255290   462043319  1750255290   462043319 "/home/<USER>/asic_learn/sim_build/hwpe_stream_basic/Vtop_hwpe_stream_intf_stream__D20__Slow.cpp"
T      1110   194584  1750255290   462043319  1750255290   462043319 "/home/<USER>/asic_learn/sim_build/hwpe_stream_basic/Vtop_hwpe_stream_package.h"
T       511   194596  1750255290   462043319  1750255290   462043319 "/home/<USER>/asic_learn/sim_build/hwpe_stream_basic/Vtop_hwpe_stream_package__DepSet_h7a78b85c__0__Slow.cpp"
T      1221   194595  1750255290   462043319  1750255290   462043319 "/home/<USER>/asic_learn/sim_build/hwpe_stream_basic/Vtop_hwpe_stream_package__Slow.cpp"
S      3314   169715  1750255287   186043943  1750255287   186043943 "/home/<USER>/asic_learn/tech_cells_generic/src/rtl/tc_clk.sv"
S  16380528    96572  1747752339   292052954  1747752339   292052954 "/usr/local/bin/verilator_bin"
S      6525    96654  1747752339   568052902  1747752339   568052902 "/usr/local/share/verilator/include/verilated_std.sv"
S      2787    96636  1747752339   564052903  1747752339   564052903 "/usr/local/share/verilator/include/verilated_std_waiver.vlt"
