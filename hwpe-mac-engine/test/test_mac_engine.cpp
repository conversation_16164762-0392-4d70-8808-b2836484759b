/*
 * test_mac_engine.cpp
 * Comprehensive testbench for MAC Engine Wrapper
 * 
 * This file provides a complete test environment for the MAC engine
 * including driver functions, test scenarios, and waveform generation.
 */

#include <iostream>
#include <iomanip>
#include <memory>
#include <vector>
#include <random>
#include <cassert>
#include <cstdint>
#include <string>

// Verilator includes
#include "verilated.h"
#include "verilated_vcd_c.h"

// Include generated model header
#include "mac_engine.h"

// MAC engine package constants (from mac_package.sv)
#define MAC_CNT_LEN 1024

// Control register bit fields (from mac_package.sv ctrl_engine_t)
struct CtrlEngine {
    bool clear;
    bool enable;
    bool simple_mul;
    bool start;
    uint8_t shift;      // 5 bits for shift (log2(32))
    uint16_t len;       // 11 bits for length (log2(MAC_CNT_LEN)+1)

    uint32_t pack() const {
        uint32_t packed = 0;
        // According to instruction: lowest bit is len, then shift
        // len (11 bits) at bits 0-10
        packed |= (static_cast<uint32_t>(len) & 0x7FF) << 0;
        // shift (5 bits) at bits 11-15
        packed |= (static_cast<uint32_t>(shift) & 0x1F) << 11;
        // start (1 bit) at bit 16
        packed |= (start ? 1 : 0) << 16;
        // simple_mul (1 bit) at bit 17
        packed |= (simple_mul ? 1 : 0) << 17;
        // enable (1 bit) at bit 18
        packed |= (enable ? 1 : 0) << 18;
        // clear (1 bit) at bit 19
        packed |= (clear ? 1 : 0) << 19;
        return packed;
    }
};

// Flags register bit fields (from mac_package.sv flags_engine_t)
struct FlagsEngine {
    uint16_t cnt;       // 11 bits
    bool acc_valid;     // 1 bit
    
    static FlagsEngine unpack(uint16_t flags) {
        FlagsEngine f;
        f.cnt = flags & 0x7FF;
        f.acc_valid = (flags >> 11) & 0x1;
        return f;
    }
};

/**
 * MAC Engine Test Driver Class
 * Provides high-level driver functions for testing the MAC engine
 */
class MacEngineTestDriver {
private:
    std::unique_ptr<mac_engine> dut;
    std::unique_ptr<VerilatedVcdC> tfp;
    uint64_t sim_time;
    uint64_t posedge_cnt;

public:
    MacEngineTestDriver(const std::string& vcd_filename = "mac_engine_test.vcd") 
        : sim_time(0), posedge_cnt(0) {
        
        // Create DUT instance
        dut = std::make_unique<mac_engine>("mac_engine_wrapper");
        
        // Initialize trace dump
        tfp = std::make_unique<VerilatedVcdC>();
        Verilated::traceEverOn(true);
        dut->trace(tfp.get(), 99);
        tfp->open(vcd_filename.c_str());
        
        // Initialize all inputs
        reset_inputs();
    }
    
    ~MacEngineTestDriver() {
        if (tfp) {
            tfp->close();
        }
        dut->final();
    }

    // Public getter for DUT access in test functions
    mac_engine* get_dut() { return dut.get(); }

    void reset_inputs() {
        dut->clk_i = 0;
        dut->rst_ni = 1;
        dut->test_mode_i = 0;
        
        // Input stream A
        dut->a_valid_i = 0;
        dut->a_data_i = 0;
        dut->a_strb_i = 0xF;
        
        // Input stream B
        dut->b_valid_i = 0;
        dut->b_data_i = 0;
        dut->b_strb_i = 0xF;
        
        // Input stream C
        dut->c_valid_i = 0;
        dut->c_data_i = 0;
        dut->c_strb_i = 0xF;
        
        // Output stream D
        dut->d_ready_i = 0;
        
        // Control
        dut->ctrl_i = 0;
    }

    void clock_cycle() {
        // Rising edge
        dut->clk_i = 0;
        dut->eval();
        tfp->dump(sim_time++);
        
        // Falling edge 
        dut->clk_i = 1;
        dut->eval();
        tfp->dump(sim_time++);
        
        posedge_cnt++;
    }

    void half_clock_cycle() {
        dut->clk_i = 0;
        dut->eval();
        tfp->dump(sim_time++);
    }

    void top_eval() {
        dut->eval();
    }

    void reset_dut(int cycles = 5) {
        std::cout << "Resetting DUT for " << cycles << " cycles..." << std::endl;
        dut->rst_ni = 0;
        for (int i = 0; i < cycles; i++) {
            clock_cycle();
        }
        dut->rst_ni = 1;
        std::cout << "Reset complete." << std::endl;
    }

    void wait_cycles(int cycles) {
        for (int i = 0; i < cycles; i++) {
            clock_cycle();
        }
    }

    void set_control(const CtrlEngine& ctrl) {
        dut->ctrl_i = ctrl.pack();
        std::cout << std::hex << "Setting control: 0x" << ctrl.pack() 
                  << " (clear=" << ctrl.clear 
                  << ", enable=" << ctrl.enable
                  << ", simple_mul=" << ctrl.simple_mul
                  << ", start=" << ctrl.start
                  << ", shift=" << std::dec << (int)ctrl.shift
                  << ", len=" << ctrl.len << ")" << std::endl;
    }

    FlagsEngine get_flags() {
        return FlagsEngine::unpack(dut->flags_o);
    }

    // Updated send_stream_data for better streaming and simple_mul handling
    void send_stream_data(uint32_t a_data, uint32_t b_data, uint32_t c_data, 
                         bool is_simple_mul, bool& transaction_occurred) {
        transaction_occurred = false;

        // Set data signals irrespective of valid
        dut->a_data_i = a_data;
        dut->b_data_i = b_data;
        dut->c_data_i = c_data; // C data is always provided, DUT decides to use it

        // Assert valid signals
        dut->a_valid_i = 1;
        dut->b_valid_i = 1;
        if (!is_simple_mul) { // Only assert c_valid if not simple_mul, or if specifically needed
            dut->c_valid_i = 1;
        } else {
            dut->c_valid_i = 0; // In simple_mul, c is generally not used for data stream
        }

        // Check for ready signals
        // For simple_mul, only a_ready_o and b_ready_o matter for the primary transaction
        // For MAC mode, all a, b, c ready signals matter
        bool ready_for_transaction = false;
        if (is_simple_mul) {
            ready_for_transaction = dut->a_ready_o && dut->b_ready_o;
        } else {
            ready_for_transaction = dut->a_ready_o && dut->b_ready_o && dut->c_ready_o;
        }

        if (ready_for_transaction) {
            transaction_occurred = true; // Handshake will occur this cycle
            // Data is latched by DUT on this rising edge, valid will be de-asserted next cycle by caller if needed
        } else {
            // If not ready, valid signals remain asserted, data remains stable
        }
        // Note: clock_cycle() is called by the main loop, not here, to allow combinational ready assessment
        top_eval();
    }

    void post_send_cleanup() {
        // De-assert valids after a transaction or if the sender gives up
        dut->a_valid_i = 0;
        dut->b_valid_i = 0;
        dut->c_valid_i = 0;
    }

    // Updated receive_stream_data for better streaming
    uint32_t receive_stream_data(bool& transaction_occurred) {
        transaction_occurred = false;
        uint32_t result = 0;

        dut->d_ready_i = 1; // Assert ready to receive
        
        if (dut->d_valid_o) {
            result = dut->d_data_o;
            transaction_occurred = true; // Handshake will occur this cycle
            // Data is latched by testbench on this rising edge
        } else {
            // If not valid, ready remains asserted
        }
        // Note: clock_cycle() is called by the main loop, not here
        return result;
    }

    void post_receive_cleanup() {
        dut->d_ready_i = 0; // De-assert ready after attempting a receive
    }

    void print_status() {
        FlagsEngine flags = get_flags();
        std::cout << "Status - Time: " << posedge_cnt 
                  << ", Count: " << flags.cnt
                  << ", Acc Valid: " << flags.acc_valid
                  << ", A Ready: " << (int)dut->a_ready_o
                  << ", B Ready: " << (int)dut->b_ready_o  
                  << ", C Ready: " << (int)dut->c_ready_o
                  << ", D Valid: " << (int)dut->d_valid_o << std::endl;
    }

    // Test helper: Send a sequence of data and collect results
    std::vector<uint32_t> run_mac_operation(const std::vector<uint32_t>& a_data,
                                           const std::vector<uint32_t>& b_data,
                                           const std::vector<uint32_t>& c_data,
                                            CtrlEngine& ctrl) {
        std::vector<uint32_t> results;
        size_t input_idx = 0;
        size_t received_count = 0;
        size_t expected_results = ctrl.len; // Assuming len dictates number of outputs for simplicity here
                                          // For MAC, one result after len operations. For simple_mul, len results.
        if (ctrl.simple_mul) {
            // For simple_mul, one output per input triplet if len matches input size.
            // If len is less than input size, it acts as a counter for operations.
            expected_results = std::min((size_t)ctrl.len, a_data.size());
        } else {
            // For MAC, it's usually one accumulated result after 'len' operations.
            expected_results = 1; 
        }


        assert(a_data.size() == b_data.size() && b_data.size() == c_data.size());
        
        // Set control registers
        set_control(ctrl);
        // If clearing, allow a couple of cycles for it to take effect if it's sequential.
        // The actual mac_engine.sv clears r_acc and r_cnt combinationally on ctrl_i.clear assertion if rst_ni is high.
        // However, a start signal might also re-initialize counters or state.
        // We'll give one cycle after setting control before starting operations.
        clock_cycle(); 
        ctrl.start = 0; 
        set_control(ctrl);
        bool send_transaction_occurred, receive_transaction_occurred;

        // Main simulation loop: continue as long as there's data to send or results to receive
        int max_cycles = (a_data.size() + expected_results + 20) * 2; // Heuristic for timeout
        int current_cycle = 0;

        while ((input_idx < a_data.size() || (ctrl.simple_mul && received_count < expected_results) || (!ctrl.simple_mul && !get_flags().acc_valid && received_count < expected_results) ) && current_cycle < max_cycles) {
            // Attempt to send data if there is any left
            if (input_idx < a_data.size()) {
                // In simple_mul mode, we only drive C if specifically intended (e.g. for a different operation)
                // For this generic helper, assume c_data[input_idx] is relevant if not simple_mul
                send_stream_data(a_data[input_idx], b_data[input_idx], c_data[input_idx], 
                                 ctrl.simple_mul, send_transaction_occurred);
            } else {
                send_transaction_occurred = false; // No more data to send
                // Keep valids low if no data to send
                post_send_cleanup();
            }

            // Attempt to receive data
            uint32_t result = receive_stream_data(receive_transaction_occurred);

            // Advance clock by one full cycle (posedge + negedge)
            clock_cycle(); 

            // Post-cycle cleanup and state update
            if (send_transaction_occurred) {
                std::cout << "Cycle " << posedge_cnt << ": Sent data[" << input_idx << "]: A=0x" << std::hex << a_data[input_idx] 
                          << ", B=0x" << b_data[input_idx] << ", C=0x" << c_data[input_idx] << std::dec << std::endl;
                input_idx++;
                // De-assert valids for next cycle if this was the last piece of data or per stream protocol
                if (input_idx == a_data.size()) {
                     post_send_cleanup(); // Clean up immediately after last send attempt
                }
            } else if (input_idx < a_data.size()) {
                // If send didn't occur, valids remain asserted by send_stream_data, data is stable
            }

            if (receive_transaction_occurred) {
                results.push_back(result);
                received_count++;
                std::cout << "Cycle " << posedge_cnt << ": Received result[" << received_count-1 << "]: 0x" 
                          << std::hex << result << std::dec << std::endl;
            }
            // Always de-assert d_ready_i after attempting a receive for the next cycle's eval
            post_receive_cleanup(); 
            
            print_status();

            // Termination condition for MAC mode (non-simple_mul)
            if (!ctrl.simple_mul && get_flags().acc_valid && received_count >= expected_results) {
                std::cout << "MAC operation: acc_valid asserted and expected result received." << std::endl;
                break;
            }
            // Termination for simple_mul mode
            if (ctrl.simple_mul && received_count >= expected_results && input_idx >= a_data.size()) {
                 std::cout << "Simple_mul operation: all inputs sent and expected results received." << std::endl;
                 break;
            }
            current_cycle++;
        }
         if (current_cycle >= max_cycles) {
            std::cerr << "Warning: Test loop exited due to max_cycles limit." << std::endl;
        }
        
        // Ensure all valids are low at the end if not already handled
        post_send_cleanup();
        post_receive_cleanup();
        wait_cycles(5); // Some quiescent cycles

        return results;
    }
};

/**
 * Test Case Functions
 */

// Test 1: Basic reset and initialization
void test_reset_and_init(MacEngineTestDriver& driver) {
    std::cout << "\n=== Test 1: Reset and Initialization ===" << std::endl;
    
    driver.reset_dut(10);
    driver.wait_cycles(5);
    
    // Check initial state
    FlagsEngine flags = driver.get_flags();
    std::cout << "Initial flags - cnt: " << flags.cnt 
              << ", acc_valid: " << flags.acc_valid << std::endl;
    
    driver.print_status();
    std::cout << "Reset test completed.\n" << std::endl;
}

// Test 2: Simple multiplication test
void test_simple_multiplication(MacEngineTestDriver& driver) {
    std::cout << "\n=== Test 2: Simple Multiplication ===" << std::endl;
    
    CtrlEngine ctrl;
    ctrl.clear = false;
    ctrl.enable = true;
    ctrl.simple_mul = true;  // Enable simple multiplication mode
    ctrl.start = true;
    ctrl.shift = 0;
    ctrl.len = 4;  // Process 4 elements
    std::vector<uint32_t> a_data = {0x00000002, 0x00000003, 0x00000004, 0x00000005};
    std::vector<uint32_t> b_data = {0x00000003, 0x00000004, 0x00000005, 0x00000006};
    std::vector<uint32_t> c_data = {0x00000001, 0x00000002, 0x00000003, 0x00000004};
    
    auto results = driver.run_mac_operation(a_data, b_data, c_data, ctrl);
    
    std::cout << "Simple multiplication test completed with " 
              << results.size() << " results." << std::endl;
}

// Test 3: MAC (Multiply-Accumulate) operation test
void test_mac_operation(MacEngineTestDriver& driver) {
    std::cout << "\n=== Test 3: MAC Operation ===" << std::endl;
    
    CtrlEngine ctrl;
    ctrl.clear = true;  // Clear accumulator first
    ctrl.enable = true;
    ctrl.simple_mul = false;  // Enable MAC mode
    ctrl.start = true;
    ctrl.shift = 0;
    ctrl.len = 8;
    
    // First clear the accumulator
    driver.set_control(ctrl);
    driver.wait_cycles(5);
    
    // Then start MAC operation
    ctrl.clear = false;
    ctrl.start = true;
    
    std::vector<uint32_t> a_data = {0x00000001, 0x00000002, 0x00000003, 0x00000004,
                                   0x00000005, 0x00000006, 0x00000007, 0x00000008};
    std::vector<uint32_t> b_data = {0x00000002, 0x00000002, 0x00000002, 0x00000002,
                                   0x00000002, 0x00000002, 0x00000002, 0x00000002};
    std::vector<uint32_t> c_data = {0x00000000, 0x00000000, 0x00000000, 0x00000000,
                                   0x00000000, 0x00000000, 0x00000000, 0x00000000};
    
    auto results = driver.run_mac_operation(a_data, b_data, c_data, ctrl);
    
    std::cout << "MAC operation test completed with " 
              << results.size() << " results." << std::endl;
}

// Test 4: Shift operation test
void test_shift_operation(MacEngineTestDriver& driver) {
    std::cout << "\n=== Test 4: Shift Operation ===" << std::endl;
    
    CtrlEngine ctrl;
    ctrl.clear = false;
    ctrl.enable = true;
    ctrl.simple_mul = true;
    ctrl.start = true;
    ctrl.shift = 2;  // Right shift by 2 bits
    ctrl.len = 4;
    
    std::vector<uint32_t> a_data = {0x00000010, 0x00000020, 0x00000040, 0x00000080};
    std::vector<uint32_t> b_data = {0x00000004, 0x00000004, 0x00000004, 0x00000004};
    std::vector<uint32_t> c_data = {0x00000000, 0x00000000, 0x00000000, 0x00000000};
    
    auto results = driver.run_mac_operation(a_data, b_data, c_data, ctrl);
    
    std::cout << "Shift operation test completed with " 
              << results.size() << " results." << std::endl;
}

// Test 5: Random data stress test
void test_random_data(MacEngineTestDriver& driver) {
    std::cout << "\n=== Test 5: Random Data Stress Test ===" << std::endl;
    
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<uint32_t> dis(0, 0xFFFF);
    
    CtrlEngine ctrl;
    ctrl.clear = true;
    ctrl.enable = true;
    ctrl.simple_mul = false;
    ctrl.start = true;
    ctrl.shift = 0;
    ctrl.len = 16;
    
    std::vector<uint32_t> a_data, b_data, c_data;
    for (int i = 0; i < 16; i++) {
        a_data.push_back(dis(gen));
        b_data.push_back(dis(gen));
        c_data.push_back(dis(gen));
    }
    
    auto results = driver.run_mac_operation(a_data, b_data, c_data, ctrl);
    
    std::cout << "Random data stress test completed with " 
              << results.size() << " results." << std::endl;
}

// Test 6: Back-pressure and flow control test
void test_flow_control(MacEngineTestDriver& driver) {
    std::cout << "\n=== Test 6: Flow Control Test ===" << std::endl;
    
    CtrlEngine ctrl;
    ctrl.clear = false;
    ctrl.enable = true;
    ctrl.simple_mul = true;
    ctrl.start = true;
    ctrl.shift = 0;
    ctrl.len = 4;
    
    driver.set_control(ctrl);
    driver.wait_cycles(2);
    
    // Test with delayed ready signals
    for (int i = 0; i < 4; i++) {
        std::cout << "Sending data with flow control test " << i << std::endl;
        
        // Set data but don't wait for ready immediately
        driver.get_dut()->a_data_i = 0x10 + i;
        driver.get_dut()->a_valid_i = 1;
        driver.get_dut()->b_data_i = 0x20 + i;
        driver.get_dut()->b_valid_i = 1;
        driver.get_dut()->c_data_i = 0x30 + i;
        driver.get_dut()->c_valid_i = 1;
        
        // Wait a few cycles before checking ready
        driver.wait_cycles(3);
        
        // Check if ready and complete transaction
        if (driver.get_dut()->a_ready_o && driver.get_dut()->b_ready_o && driver.get_dut()->c_ready_o) {
            std::cout << "All streams ready, completing transaction" << std::endl;
            driver.clock_cycle();
        } else {
            std::cout << "Streams not ready: A=" << (int)driver.get_dut()->a_ready_o 
                      << ", B=" << (int)driver.get_dut()->b_ready_o 
                      << ", C=" << (int)driver.get_dut()->c_ready_o << std::endl;
        }
        
        // Clear valid signals
        driver.get_dut()->a_valid_i = 0;
        driver.get_dut()->b_valid_i = 0;
        driver.get_dut()->c_valid_i = 0;
        
        driver.wait_cycles(2);
        driver.print_status();
    }
    
    std::cout << "Flow control test completed." << std::endl;
}

/**
 * Main test function
 */
int main(int argc, char** argv) {
    std::cout << "Starting MAC Engine Wrapper Test" << std::endl;
    std::cout << "=================================" << std::endl;
    
    // Command line parsing
    Verilated::commandArgs(argc, argv);
    
    // Create test driver with waveform output
    MacEngineTestDriver driver("mac_engine_test.vcd");
    
    try {
        // Run all tests
        test_reset_and_init(driver);
        test_simple_multiplication(driver);
        test_mac_operation(driver);
        test_shift_operation(driver);
        test_random_data(driver);
        test_flow_control(driver);
        
        // Final status
        std::cout << "\n=== Final Status ===" << std::endl;
        driver.print_status();
        driver.wait_cycles(10);
        
        std::cout << "\nAll tests completed successfully!" << std::endl;
        std::cout << "Waveform saved to: mac_engine_test.vcd" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
