# 
# code.yml
# <PERSON> <<EMAIL>>
#
# Copyright (C) 2018 ETH Zurich, University of Bologna
# Copyright and related rights are licensed under the Solderpad Hardware
# License, Version 0.51 (the "License"); you may not use this file except in
# compliance with the License.  You may obtain a copy of the License at
# http://solderpad.org/licenses/SHL-0.51. Unless required by applicable law
# or agreed to in writing, software, hardware and materials distributed under
# this License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
# CONDITIONS OF ANY KIND, either express or implied. See the License for the
# specific language governing permissions and limitations under the License.
#

# LOOP0 loop0: for i in range(0,nb_iter)
# LOOP1 unused
# LOOP2 unused
# LOOP3 unused
# LOOP4 unused
# LOOP5 unused

# mnemonics to simplify microcode writing
mnemonics:
    a:           0
    b:           1
    c:           2
    d:           3
    nb_iter:     4
    iter_stride: 5
    one_stride:  6

# actual microcode
code:
  loop0:
    - { op : add, a : a, b : iter_stride, } # move to next subset of a
    - { op : add, a : b, b : iter_stride, } # move to next subset of b
    - { op : add, a : c, b : one_stride,  } # move to next subset of c
    - { op : add, a : d, b : one_stride,  } # move to next subset of d
