# MAC Engine 架构分析

## 核心组件

### 1. 乘法器 (Multiplier)
```systemverilog
mult = $signed(a_i.data) * $signed(b_i.data);
```
- 32位×32位有符号乘法器
- 输出64位乘积结果

### 2. 乘法流水线寄存器 (r_mult)
- 存储乘法器输出结果
- 具有valid/ready握手机制
- 支持反压控制

### 3. 累加器 (r_acc) 
- 64+$clog2(MAC_CNT_LEN)位宽累加器
- 防止累加溢出
- 支持三种操作模式：
  - 初始化：r_acc = c_shifted
  - 初始化+累加：r_acc = c_shifted + r_mult  
  - 纯累加：r_acc = r_acc + r_mult

### 4. 控制计数器 (r_cnt)
- 控制标量积模式的累加次数
- 范围：0 到 ctrl_i.len

### 5. 移位逻辑
- 输入移位：c_shifted = c_i << shift（左移，扩展精度）
- 输出移位：d_o = result >> shift（右移，恢复精度）

## 数据流向

```
a_i ──┐
      ├──> mult ──> r_mult ──┐
b_i ──┘                     │
                            ├──> r_acc ──> d_o
c_i ──> c_shifted ──────────┘
```

## 接口定义

### HWPE Stream接口
MAC引擎使用HWPE(Hardware Processing Element) Stream接口进行数据传输，定义在`hwpe_stream_interfaces.sv`中：

```systemverilog
interface hwpe_stream_intf_stream (
  input logic clk
);
  parameter int unsigned DATA_WIDTH = 32;
  parameter int unsigned STRB_WIDTH = DATA_WIDTH/8;

  logic                    valid;  // 数据有效信号
  logic                    ready;  // 接收准备信号  
  logic [DATA_WIDTH-1:0]   data;   // 数据总线
  logic [STRB_WIDTH-1:0]   strb;   // 字节使能

  modport source (output valid, data, strb, input ready);
  modport sink   (input valid, data, strb, output ready);
endinterface
```

### 端口连接
- `a_i`, `b_i`, `c_i`: sink模式的输入流
- `d_o`: source模式的输出流

**注意**: 当前linter错误是因为缺少`hwpe_stream_interfaces.sv`的包含路径。

## 控制信号

### ctrl_engine_t 结构体
- `clear`: 软复位信号
- `enable`: 使能信号  
- `simple_mul`: 模式选择（1=简单乘法，0=标量积）
- `start`: 开始信号
- `shift`: 移位位数[0:4]
- `len`: 操作长度[0:1024]

### flags_engine_t 结构体  
- `cnt`: 当前计数值
- `acc_valid`: 累加器有效标志

## 握手机制

采用**AXI-Stream风格的valid/ready握手**：
- `valid`: 数据有效信号
- `ready`: 接收准备信号
- 握手成功条件：`valid & ready = 1`

### 反压传播规则
1. `ready`信号可以依赖当前`valid`状态
2. `valid`信号不能依赖当前`ready`状态  
3. `valid`从1到0的转换必须在握手成功后发生

```systemverilog
// 反压信号分配
assign r_acc_ready  = d_o.ready | ~r_acc_valid;
assign r_mult_ready = (ctrl_i.simple_mul) ? d_o.ready | ~r_mult_valid 
                                          : r_acc_ready | ~r_mult_valid;
assign a_i.ready = (r_mult_ready & a_i.valid & b_i.valid) | (~a_i.valid & ~b_i.valid);
assign b_i.ready = (r_mult_ready & a_i.valid & b_i.valid) | (~a_i.valid & ~b_i.valid);
assign c_i.ready = r_acc_ready | ~c_i.valid;
```

## WaveDrom时序图JSON

### Simple Multiplication Mode
```json
{
  "signal": [
    {"name": "clk_i", "wave": "p......"},
    {"name": "rst_ni", "wave": "01....."},
    {"name": "ctrl_i.enable", "wave": "01....."},
    {"name": "ctrl_i.simple_mul", "wave": "01....."},
    {"name": "a_i.valid", "wave": "0.1.0.."},
    {"name": "a_i.data", "wave": "x.3.x..", "data": ["A1"]},
    {"name": "a_i.ready", "wave": "0.1.0.."},
    {"name": "b_i.valid", "wave": "0.1.0.."},
    {"name": "b_i.data", "wave": "x.3.x..", "data": ["B1"]},
    {"name": "b_i.ready", "wave": "0.1.0.."},
    {"name": "r_mult_valid", "wave": "0..1.0."},
    {"name": "r_mult", "wave": "x...4..", "data": ["A1*B1"]},
    {"name": "d_o.valid", "wave": "0..1.0."},
    {"name": "d_o.ready", "wave": "0..1..."},
    {"name": "d_o.data", "wave": "x...5..", "data": ["(A1*B1)>>shift"]}
  ],
  "config": {"hscale": 2},
  "head": {"text": "Simple Multiplication Mode"}
}
```

### Scalar Product Mode  
```json
{
  "signal": [
    {"name": "clk_i", "wave": "p.........."},
    {"name": "rst_ni", "wave": "01........."},
    {"name": "ctrl_i.enable", "wave": "01........."},
    {"name": "ctrl_i.simple_mul", "wave": "00........."},
    {"name": "ctrl_i.start", "wave": "0.1.0......"},
    {"name": "ctrl_i.len", "wave": "2..........", "data": ["3"]},
    {"name": "c_i.valid", "wave": "0.1.0......"},
    {"name": "c_i.data", "wave": "x.3.x......", "data": ["C0"]},
    {"name": "c_i.ready", "wave": "0.1.0......"},
    {"name": "a_i.valid", "wave": "*******.0.."},
    {"name": "a_i.data", "wave": "x.3.4.5.x..", "data": ["A1", "A2", "A3"]},
    {"name": "a_i.ready", "wave": "*******.0.."},
    {"name": "b_i.valid", "wave": "*******.0.."},
    {"name": "b_i.data", "wave": "x.3.4.5.x..", "data": ["B1", "B2", "B3"]},
    {"name": "b_i.ready", "wave": "*******.0.."},
    {"name": "r_cnt", "wave": "2..3.4.5...", "data": ["0", "1", "2", "3"]},
    {"name": "r_mult_valid", "wave": "0..1.1.1.0."},
    {"name": "r_acc", "wave": "x...3.4.5..", "data": ["C0+(A1*B1)", "C0+(A1*B1)+(A2*B2)", "C0+(A1*B1)+(A2*B2)+(A3*B3)"]},
    {"name": "r_acc_valid", "wave": "0......1.0."},
    {"name": "d_o.valid", "wave": "0......1.0."},
    {"name": "d_o.ready", "wave": "0......1..."},
    {"name": "d_o.data", "wave": "x......6...", "data": ["result>>shift"]}
  ],
  "config": {"hscale": 1.5},
  "head": {"text": "Scalar Product Mode (len=3)"}
}
```

### 反压示例
```json
{
  "signal": [
    {"name": "clk_i", "wave": "p........"},
    {"name": "a_i.valid", "wave": "*******.."},
    {"name": "a_i.ready", "wave": "*******.."},
    {"name": "a_i.data", "wave": "x.3.3.4..", "data": ["A1", "A1", "A2"]},
    {"name": "d_o.valid", "wave": "0...1.1.."},
    {"name": "d_o.ready", "wave": "0...1.0.."},
    {"name": "d_o.data", "wave": "x...5.5..", "data": ["D1", "D1"]},
    {},
    {"name": "note", "wave": "x........", "data": ["", "", "backpressure", "stall", "resume", "", "", ""]}
  ],
  "config": {"hscale": 1.5},
  "head": {"text": "Backpressure Example"}
}
```

## 操作逻辑详解

### Simple Multiplication模式流程
1. **初始化**: 设置`ctrl_i.simple_mul = 1`, `ctrl_i.enable = 1`
2. **数据输入**: a_i和b_i同时提供有效数据
3. **乘法计算**: mult = a_i.data × b_i.data (组合逻辑)
4. **流水线寄存**: 乘法结果在下个时钟存入r_mult寄存器
5. **移位输出**: d_o.data = r_mult >> ctrl_i.shift

**关键特点**:
- 延迟: 2个时钟周期 (乘法器→寄存器→输出)
- 吞吐量: 每个时钟周期可接受新的a_i, b_i输入对

### Scalar Product模式流程  
1. **初始化**: 设置`ctrl_i.simple_mul = 0`, `ctrl_i.len = N`, `ctrl_i.start = 1`
2. **累加器预加载**: 
   - c_shifted = c_i.data << ctrl_i.shift
   - r_acc = c_shifted (当c_i.valid & c_i.ready时)
3. **循环累加**: 
   - 计数器从0递增到len
   - 每次有效的r_mult输入: r_acc += r_mult
   - 条件: r_mult_valid & r_mult_ready
4. **完成判断**: 当r_cnt == ctrl_i.len时，设置r_acc_valid = 1
5. **移位输出**: d_o.data = r_acc >> ctrl_i.shift

**关键特点**:
- 延迟: 2 + len个时钟周期
- 累加器位宽: 64 + $clog2(MAC_CNT_LEN) = 74位 (防溢出)

### 定点数处理
MAC引擎支持任意定点格式的数据处理:
- **输入扩展**: c_i左移shift位，匹配累加器的定点格式
- **输出截断**: 结果右移shift位，恢复输出的定点格式
- **精度保持**: 中间计算使用更高精度防止溢出

### 状态机集成
MAC引擎通过flags_o向上层控制器报告状态:
- `flags_o.cnt`: 当前累加计数
- `flags_o.acc_valid`: 累加完成标志

上层FSM可据此判断操作完成并启动下一轮计算。

### 关键时序特点
- **2级流水线**: 乘法器 → 累加器  
- **自定时**: 基于valid/ready握手机制
- **反压支持**: 下游ready信号控制上游数据流
- **零气泡**: 连续数据流不会产生流水线气泡
- **可配置精度**: 支持移位的定点数运算

这种设计既支持高吞吐量的流式计算，又保持了灵活的控制机制，适合用于机器学习和数字信号处理的加速器设计。

## 编译注意事项

**Linter错误解决**:
当前MAC引擎需要包含以下文件才能正确编译:
```systemverilog
`include "hwpe_stream_interfaces.sv"  // 提供hwpe_stream_intf_stream接口定义
`include "hwpe_stream_package.sv"     // 提供stream相关类型定义
`include "mac_package.sv"             // 提供控制和标志类型定义
```

或在编译时指定正确的包含路径和依赖顺序。