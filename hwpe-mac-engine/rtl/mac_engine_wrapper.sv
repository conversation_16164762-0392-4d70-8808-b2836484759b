/*
 * mac_engine_wrapper.sv
 * Wrapper for mac_engine.sv that removes interface dependencies
 * 
 * This wrapper converts HWPE Stream interfaces to standard SystemVerilog ports
 * for easier integration and reduced compilation dependencies.
 *
 * Copyright (C) 2024
 * Licensed under the same terms as the original mac_engine.sv
 */

import mac_package::*;

module mac_engine_wrapper
#(
  parameter int unsigned DATA_WIDTH = 32,
  parameter int unsigned STRB_WIDTH = DATA_WIDTH/8
)
(
  // Global signals
  input  logic                   clk_i,
  input  logic                   rst_ni,
  input  logic                   test_mode_i,
  
  // Input stream A (converted from hwpe_stream_intf_stream.sink)
  input  logic                   a_valid_i,
  output logic                   a_ready_o,
  input  logic [DATA_WIDTH-1:0]  a_data_i,
  input  logic [STRB_WIDTH-1:0]  a_strb_i,
  
  // Input stream B (converted from hwpe_stream_intf_stream.sink)
  input  logic                   b_valid_i,
  output logic                   b_ready_o,
  input  logic [DATA_WIDTH-1:0]  b_data_i,
  input  logic [STRB_WIDTH-1:0]  b_strb_i,
  
  // Input stream C (converted from hwpe_stream_intf_stream.sink)
  input  logic                   c_valid_i,
  output logic                   c_ready_o,
  input  logic [DATA_WIDTH-1:0]  c_data_i,
  input  logic [STRB_WIDTH-1:0]  c_strb_i,
  
  // Output stream D (converted from hwpe_stream_intf_stream.source)
  output logic                   d_valid_o,
  input  logic                   d_ready_i,
  output logic [DATA_WIDTH-1:0]  d_data_o,
  output logic [STRB_WIDTH-1:0]  d_strb_o,
  
  // Control and status
  input  ctrl_engine_t           ctrl_i,
  output flags_engine_t          flags_o
);

  // Internal interface instances
  hwpe_stream_intf_stream #(.DATA_WIDTH(DATA_WIDTH)) a_intf (.clk(clk_i));
  hwpe_stream_intf_stream #(.DATA_WIDTH(DATA_WIDTH)) b_intf (.clk(clk_i));
  hwpe_stream_intf_stream #(.DATA_WIDTH(DATA_WIDTH)) c_intf (.clk(clk_i));
  hwpe_stream_intf_stream #(.DATA_WIDTH(DATA_WIDTH)) d_intf (.clk(clk_i));

  // Connect input stream A ports to interface
  assign a_intf.valid = a_valid_i;
  assign a_ready_o    = a_intf.ready;
  assign a_intf.data  = a_data_i;
  assign a_intf.strb  = a_strb_i;
  
  // Connect input stream B ports to interface
  assign b_intf.valid = b_valid_i;
  assign b_ready_o    = b_intf.ready;
  assign b_intf.data  = b_data_i;
  assign b_intf.strb  = b_strb_i;
  
  // Connect input stream C ports to interface
  assign c_intf.valid = c_valid_i;
  assign c_ready_o    = c_intf.ready;
  assign c_intf.data  = c_data_i;
  assign c_intf.strb  = c_strb_i;
  
  // Connect output stream D interface to ports
  assign d_valid_o    = d_intf.valid;
  assign d_intf.ready = d_ready_i;
  assign d_data_o     = d_intf.data;
  assign d_strb_o     = d_intf.strb;

  // Instantiate the original MAC engine
  mac_engine i_mac_engine (
    .clk_i       ( clk_i       ),
    .rst_ni      ( rst_ni      ),
    .test_mode_i ( test_mode_i ),
    .a_i         ( a_intf.sink ),
    .b_i         ( b_intf.sink ),
    .c_i         ( c_intf.sink ),
    .d_o         ( d_intf.source ),
    .ctrl_i      ( ctrl_i      ),
    .flags_o     ( flags_o     )
  );

endmodule // mac_engine_wrapper 