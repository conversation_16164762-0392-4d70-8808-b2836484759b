package:
  name: hwpe-stream
  authors:
    - "<PERSON> <<EMAIL>>"
    - "<PERSON><PERSON><PERSON> <<EMAIL>>"

dependencies:
  tech_cells_generic: { git: "https://github.com/pulp-platform/tech_cells_generic.git", version: 0.2.2 }

sources:
  - include_dirs:
      - rtl
    files:
      # Source files grouped in levels. Files in level 0 have no dependencies on files in this
      # package. Files in level 1 only depend on files in level 0, files in level 2 on files in
      # levels 1 and 0, etc. Files within a level are ordered alphabetically.
      # Level 0
      - rtl/hwpe_stream_package.sv
      - rtl/hwpe_stream_interfaces.sv
      # Level 1
      - rtl/basic/hwpe_stream_assign.sv
      - rtl/basic/hwpe_stream_buffer.sv
      - rtl/basic/hwpe_stream_demux_static.sv
      - rtl/basic/hwpe_stream_deserialize.sv
      - rtl/basic/hwpe_stream_fence.sv
      - rtl/basic/hwpe_stream_merge.sv
      - rtl/basic/hwpe_stream_mux_static.sv
      - rtl/basic/hwpe_stream_serialize.sv
      - rtl/basic/hwpe_stream_split.sv
      - rtl/fifo/hwpe_stream_fifo_ctrl.sv
      - rtl/fifo/hwpe_stream_fifo_scm.sv
      - rtl/streamer/hwpe_stream_addressgen.sv
      - rtl/streamer/hwpe_stream_addressgen_v2.sv
      - rtl/streamer/hwpe_stream_addressgen_v3.sv
      - rtl/streamer/hwpe_stream_sink_realign.sv
      - rtl/streamer/hwpe_stream_source_realign.sv
      - rtl/streamer/hwpe_stream_strbgen.sv
      - rtl/streamer/hwpe_stream_streamer_queue.sv
      - rtl/tcdm/hwpe_stream_tcdm_assign.sv
      - rtl/tcdm/hwpe_stream_tcdm_mux.sv
      - rtl/tcdm/hwpe_stream_tcdm_mux_static.sv
      - rtl/tcdm/hwpe_stream_tcdm_reorder.sv
      - rtl/tcdm/hwpe_stream_tcdm_reorder_static.sv
      # Level 2
      - rtl/fifo/hwpe_stream_fifo_earlystall.sv
      - rtl/fifo/hwpe_stream_fifo_earlystall_sidech.sv
      - rtl/fifo/hwpe_stream_fifo_scm_test_wrap.sv
      - rtl/fifo/hwpe_stream_fifo_sidech.sv
      # Level 3
      - rtl/fifo/hwpe_stream_fifo.sv
      - rtl/tcdm/hwpe_stream_tcdm_fifo_load_sidech.sv
      # Level 4
      - rtl/fifo/hwpe_stream_fifo_passthrough.sv
      - rtl/streamer/hwpe_stream_source.sv
      - rtl/tcdm/hwpe_stream_tcdm_fifo.sv
      - rtl/tcdm/hwpe_stream_tcdm_fifo_load.sv
      - rtl/tcdm/hwpe_stream_tcdm_fifo_store.sv
      # Level 5
      - rtl/streamer/hwpe_stream_sink.sv
      - target: test
        files:
          - rtl/verif/hwpe_stream_traffic_gen.sv
          - rtl/verif/hwpe_stream_traffic_recv.sv
          - rtl/verif/tb_fifo.sv

