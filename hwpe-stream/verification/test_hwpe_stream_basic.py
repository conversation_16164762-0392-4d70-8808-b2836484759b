#!/usr/bin/env python3
"""
Basic test cases for HWPE-Stream protocol using the HWPE-Stream VIP.

This module demonstrates how to use the HWPE-Stream verification IP
to test various aspects of the HWPE-Stream protocol.
"""

import os
from pathlib import Path
import cocotb
from cocotb.triggers import RisingEdge, ClockCycles, Timer
from cocotb.clock import Clock
from cocotb.log import Sim<PERSON>og
import pytest

from hwpe_stream_vip import (
    HWPEStreamTransaction,
    HWPEStreamDriver,
    HWPEStreamMonitor,
    HWPEStreamSink,
    HWPEStreamSequencer
)
from cocotb.runner import get_runner
import random
import logging

class HWPEStreamScoreboard:
    """
    Simple scoreboard for comparing sent vs received transactions.
    """
    
    def __init__(self, name="HWPEStreamScoreboard"):
        self.log = SimLog(name)
        self.sent_transactions = []
        self.received_transactions = []
        self.errors = 0
        cocotb.log.setLevel(logging.DEBUG)

    def add_sent_transaction(self, transaction):
        """Add a transaction that was sent"""
        self.sent_transactions.append(transaction)
        self.log.debug(f"Added sent transaction: {transaction}")
    
    def add_received_transaction(self, transaction):
        """Add a transaction that was received"""
        self.received_transactions.append(transaction)
        self.log.debug(f"Added received transaction: {transaction}")
        self._check_transaction()
    
    def _check_transaction(self):
        """Check if the latest received transaction matches expected"""
        if len(self.received_transactions) > len(self.sent_transactions):
            self.log.error("Received more transactions than sent!")
            self.errors += 1
            return
        
        # Compare latest received with corresponding sent
        sent_idx = len(self.received_transactions) - 1
        sent_txn = self.sent_transactions[sent_idx]
        received_txn = self.received_transactions[sent_idx]
        
        if sent_txn.data != received_txn.data:
            self.log.error(f"Data mismatch! Sent: 0x{sent_txn.data:X}, "
                          f"Received: 0x{received_txn.data:X}")
            self.errors += 1
        
        if sent_txn.strb != received_txn.strb:
            self.log.error(f"Strobe mismatch! Sent: 0x{sent_txn.strb:X}, "
                          f"Received: 0x{received_txn.strb:X}")
            self.errors += 1
        
        if self.errors == 0:
            self.log.debug(f"Transaction {sent_idx} matched successfully")
    
    def check_complete(self):
        """Final check that all transactions match"""
        if len(self.sent_transactions) != len(self.received_transactions):
            self.log.error(f"Transaction count mismatch! Sent: {len(self.sent_transactions)}, "
                          f"Received: {len(self.received_transactions)}")
            self.errors += 1
        
        return self.errors == 0


@cocotb.test()
async def test_simple_stream_transfer(dut):
    """
    Test basic stream transfer functionality.
    
    This test sends a sequence of transactions through a simple
    HWPE-Stream connection and verifies they are received correctly.
    """
    log = SimLog("test_simple_stream_transfer")
    log.info("Starting simple stream transfer test")
    
    # Start the clock
    clock = Clock(dut.clk_i, 10, units="ns")
    cocotb.start_soon(clock.start())
    
    # Apply reset
    dut.rst_ni.value = 0
    dut.clear_i.value = 0
    dut.test_mode_i.value = 0
    await ClockCycles(dut.clk_i, 5)
    dut.rst_ni.value = 1
    log.info("Reset complete")
    
    # Create VIP components
    # Note: Signal names match the wrapper module interface
    driver = HWPEStreamDriver(dut, "push_i", dut.clk_i, dut.rst_ni)
    sink = HWPEStreamSink(dut, "pop_o", dut.clk_i, dut.rst_ni)
    monitor = HWPEStreamMonitor(dut, "pop_o", dut.clk_i)
    
    # Create scoreboard
    scoreboard = HWPEStreamScoreboard("StreamScoreboard")
    
    # Set monitor callback to feed scoreboard
    monitor.callback = scoreboard.add_received_transaction
    
    # Start sink and monitoring
    await sink.start_backpressure_generation()
    await monitor.start_monitoring()
    
    # Wait a few cycles for initialization
    await ClockCycles(dut.clk_i, 5)
    
    # Create test sequence
    sequencer = HWPEStreamSequencer(data_width=32)
    test_transactions = sequencer.create_counting_sequence(length=10, start_value=0x100)
    
    log.info(f"Sending {len(test_transactions)} transactions")
    
    # Send transactions
    for i, txn in enumerate(test_transactions):
        scoreboard.add_sent_transaction(txn)
        await driver.send(txn)
        log.debug(f"Sent transaction {i}: {txn}")
    
    # Wait for all transactions to be received
    await ClockCycles(dut.clk_i, 20)
    
    # Check results
    if scoreboard.check_complete():
        log.info("TEST PASSED: All transactions transferred correctly")
    else:
        log.error(f"TEST FAILED: {scoreboard.errors} errors detected")
        assert False, "Transaction verification failed"
    
    # Print statistics
    stats = monitor.get_statistics()
    log.info(f"Monitor statistics: {stats}")


@cocotb.test()
async def test_stream_backpressure(dut):
    """
    Test stream behavior under backpressure conditions.
    
    This test applies random backpressure to verify the source
    correctly handles ready signal variations.
    """
    log = SimLog("test_stream_backpressure")
    log.info("Starting stream backpressure test")
    
    # Start the clock
    clock = Clock(dut.clk_i, 10, units="ns")
    cocotb.start_soon(clock.start())
    
    # Apply reset
    dut.rst_ni.value = 0
    dut.clear_i.value = 0
    dut.test_mode_i.value = 0
    await ClockCycles(dut.clk_i, 5)
    dut.rst_ni.value = 1
    
    # Create VIP components
    driver = HWPEStreamDriver(dut, "push_i", dut.clk_i, dut.rst_ni)
    sink = HWPEStreamSink(dut, "pop_o", dut.clk_i, dut.rst_ni)
    monitor = HWPEStreamMonitor(dut, "pop_o", dut.clk_i)
    
    # Configure backpressure - 30% probability of ready being low
    sink.set_backpressure_probability(0.3)
    await sink.start_backpressure_generation()
    
    # Create scoreboard
    scoreboard = HWPEStreamScoreboard("BackpressureScoreboard")
    monitor.callback = scoreboard.add_received_transaction
    await monitor.start_monitoring()
    
    await ClockCycles(dut.clk_i, 5)
    
    # Create test sequence with random data
    sequencer = HWPEStreamSequencer(data_width=32)
    test_transactions = []
    for i in range(20):
        txn = sequencer.create_random_transaction()
        test_transactions.append(txn)
    
    log.info(f"Sending {len(test_transactions)} transactions with backpressure")
    
    # Send transactions concurrently while backpressure is applied
    for i, txn in enumerate(test_transactions):
        scoreboard.add_sent_transaction(txn)
        await driver.send(txn)
        log.debug(f"Sent transaction {i} under backpressure: {txn}")
    
    # Wait longer for transactions to complete under backpressure
    await ClockCycles(dut.clk_i, 100)
    
    # Check results
    if scoreboard.check_complete():
        log.info("TEST PASSED: All transactions survived backpressure")
    else:
        log.error(f"TEST FAILED: {scoreboard.errors} errors under backpressure")
        assert False, "Backpressure test failed"
    
    # Print statistics
    stats = monitor.get_statistics()
    log.info(f"Backpressure test statistics: {stats}")


@cocotb.test()
async def test_stream_burst_transfer(dut):
    """
    Test burst transfers with configurable delays.
    
    This test sends bursts of transactions with various delay patterns
    to verify timing behavior.
    """
    log = SimLog("test_stream_burst_transfer")
    log.info("Starting stream burst transfer test")
    
    # Start the clock
    clock = Clock(dut.clk_i, 10, units="ns")
    cocotb.start_soon(clock.start())
    
    # Apply reset
    dut.rst_ni.value = 0
    await ClockCycles(dut.clk_i, 5)
    dut.rst_ni.value = 1
    
    # Create VIP components
    driver = HWPEStreamDriver(dut, "push_i", dut.clk_i, dut.rst_ni)
    sink = HWPEStreamSink(dut, "pop_o", dut.clk_i, dut.rst_ni)  # 添加sink来驱动ready信号
    monitor = HWPEStreamMonitor(dut, "pop_o", dut.clk_i)
    
    # Create scoreboard
    scoreboard = HWPEStreamScoreboard("BurstScoreboard")
    monitor.callback = scoreboard.add_received_transaction
    
    # Start sink and monitoring
    await sink.start_backpressure_generation()  # 开始驱动ready信号
    await monitor.start_monitoring()
    
    await ClockCycles(dut.clk_i, 5)
    
    # Create burst sequence with inter-transaction delays
    sequencer = HWPEStreamSequencer(data_width=32)
    burst_transactions = sequencer.create_burst_sequence(
        burst_length=15, 
        inter_burst_delay=3
    )
    
    log.info(f"Sending burst of {len(burst_transactions)} transactions")
    
    # Send burst
    for i, txn in enumerate(burst_transactions):
        scoreboard.add_sent_transaction(txn)
        await driver.send(txn)
        log.debug(f"Sent burst transaction {i}: {txn}")
    
    # Wait for completion
    await ClockCycles(dut.clk_i, 50)
    
    # Check results
    if scoreboard.check_complete():
        log.info("TEST PASSED: Burst transfer completed successfully")
    else:
        log.error(f"TEST FAILED: {scoreboard.errors} errors in burst transfer")
        assert False, "Burst transfer test failed"
    
    # Print statistics
    stats = monitor.get_statistics()
    log.info(f"Burst transfer statistics: {stats}")


@cocotb.test()
async def test_stream_strobe_patterns(dut):
    """
    Test various strobe patterns to verify byte-level control.
    
    This test sends transactions with different strobe patterns
    to verify correct handling of partial data.
    """
    log = SimLog("test_stream_strobe_patterns")
    log.info("Starting stream strobe patterns test")
    
    # Start the clock
    clock = Clock(dut.clk_i, 10, units="ns")
    cocotb.start_soon(clock.start())
    
    # Apply reset
    dut.rst_ni.value = 0
    await ClockCycles(dut.clk_i, 5)
    dut.rst_ni.value = 1
    
    # Create VIP components
    driver = HWPEStreamDriver(dut, "push_i", dut.clk_i, dut.rst_ni)
    monitor = HWPEStreamMonitor(dut, "pop_o", dut.clk_i)
    
    # Create scoreboard
    scoreboard = HWPEStreamScoreboard("StrobeScoreboard")
    monitor.callback = scoreboard.add_received_transaction
    await monitor.start_monitoring()
    
    await ClockCycles(dut.clk_i, 5)
    
    # Create test transactions with various strobe patterns
    test_transactions = []
    
    # Test different strobe patterns for 32-bit data
    strobe_patterns = [
        0b1111,  # All bytes valid
        0b0001,  # Only byte 0 valid
        0b1000,  # Only byte 3 valid
        0b1010,  # Bytes 1 and 3 valid
        0b0101,  # Bytes 0 and 2 valid
        0b1100,  # Bytes 2 and 3 valid
        0b0011,  # Bytes 0 and 1 valid
    ]
    
    for i, strb in enumerate(strobe_patterns):
        data = 0x12345678 + i  # Use recognizable data pattern
        txn = HWPEStreamTransaction(data=data, strb=strb, data_width=32)
        test_transactions.append(txn)
    
    log.info(f"Testing {len(test_transactions)} strobe patterns")
    
    # Send transactions with different strobe patterns
    for i, txn in enumerate(test_transactions):
        scoreboard.add_sent_transaction(txn)
        await driver.send(txn)
        log.debug(f"Sent strobe pattern {i}: {txn}")
        log.debug(f"  Valid bytes: {txn.get_valid_bytes()}")
    
    # Wait for completion
    await ClockCycles(dut.clk_i, 30)
    
    # Check results
    if scoreboard.check_complete():
        log.info("TEST PASSED: All strobe patterns handled correctly")
    else:
        log.error(f"TEST FAILED: {scoreboard.errors} errors in strobe handling")
        assert False, "Strobe pattern test failed"
    
    # Print statistics  
    stats = monitor.get_statistics()
    log.info(f"Strobe pattern test statistics: {stats}")



def run_test():
    # 获取当前文件的目录
    tests_dir = Path(__file__).parent
    hdl_dir = tests_dir / ".." / "rtl" 
    sim = os.getenv("SIM", "verilator")

    # 使用cocotb runner运行测试
    runner = get_runner(sim)
    runner.build(
        verilog_sources=[
            hdl_dir / "hwpe_stream_package.sv",      # 必须首先编译package
            hdl_dir / "hwpe_stream_interfaces.sv",   # 然后编译interface
            hdl_dir / "basic" / "hwpe_stream_buffer.sv",
            "/home/<USER>/asic_learn/tech_cells_generic/src/rtl/tc_clk.sv",
            tests_dir / "hwpe_stream_buffer_wrapper.sv"
        ],
        hdl_toplevel="hwpe_stream_buffer_wrapper",
        waves=True,
        build_dir="sim_build/hwpe_stream_basic"
    )
    runner.test(
        test_module="test_hwpe_stream_basic",
        hdl_toplevel="hwpe_stream_buffer_wrapper",
        waves=True
    )

if __name__ == "__main__":
    run_test()