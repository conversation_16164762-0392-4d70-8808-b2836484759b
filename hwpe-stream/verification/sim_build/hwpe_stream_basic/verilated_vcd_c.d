verilated_vcd_c.o: /usr/local/share/verilator/include/verilated_vcd_c.cpp \
 /usr/local/share/verilator/include/verilatedos.h \
 /usr/local/share/verilator/include/verilated.h \
 /usr/local/share/verilator/include/verilated_config.h \
 /usr/local/share/verilator/include/verilated_types.h \
 /usr/local/share/verilator/include/verilated_funcs.h \
 /usr/local/share/verilator/include/verilated_vcd_c.h \
 /usr/local/share/verilator/include/verilated_trace.h \
 /usr/local/share/verilator/include/verilated_trace_imp.h \
 /usr/local/share/verilator/include/verilated_intrinsics.h \
 /usr/local/share/verilator/include/verilated_threads.h
