<testsuites name="results">
  <testsuite name="all" package="all">
    <property name="random_seed" value="1750340259" />
    <testcase name="test_simple_stream_transfer" classname="test_hwpe_stream_basic" file="/home/<USER>/asic_learn/hwpe-stream/verification/test_hwpe_stream_basic.py" lineno="86" time="0.006955862045288086" sim_time_ns="290.001" ratio_time="41691.597405449866" />
    <testcase name="test_stream_backpressure" classname="test_hwpe_stream_basic" file="/home/<USER>/asic_learn/hwpe-stream/verification/test_hwpe_stream_basic.py" lineno="155" time="0.015610218048095703" sim_time_ns="1100.001" ratio_time="70466.72869083911">
      <failure message="Test failed with RANDOM_SEED=1750340259" />
    </testcase>
    <testcase name="test_stream_burst_transfer" classname="test_hwpe_stream_basic" file="/home/<USER>/asic_learn/hwpe-stream/verification/test_hwpe_stream_basic.py" lineno="223" time="0.008936166763305664" sim_time_ns="600.001" ratio_time="67142.99496555587" />
    <testcase name="test_stream_strobe_patterns" classname="test_hwpe_stream_basic" file="/home/<USER>/asic_learn/hwpe-stream/verification/test_hwpe_stream_basic.py" lineno="288" time="0.005102634429931641" sim_time_ns="400.001" ratio_time="78391.07533426782" />
  </testsuite>
</testsuites>
