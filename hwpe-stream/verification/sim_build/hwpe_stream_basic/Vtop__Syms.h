// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Symbol table internal header
//
// Internal details; most calling programs do not need this header,
// unless using verilator public meta comments.

#ifndef VERILATED_VTOP__SYMS_H_
#define VERILATED_VTOP__SYMS_H_  // guard

#include "verilated.h"

// INCLUDE MODEL CLASS

#include "Vtop.h"

// INCLUDE MODULE CLASSES
#include "Vtop___024root.h"
#include "Vtop___024unit.h"
#include "Vtop_hwpe_stream_intf_stream__D20.h"
#include "Vtop_hwpe_stream_package.h"

// DPI TYPES for DPI Export callbacks (Internal use)

// SYMS CLASS (contains all model state)
class alignas(VL_CACHE_LINE_BYTES)Vtop__Syms final : public VerilatedSyms {
  public:
    // INTERNAL STATE
    Vtop* const __Vm_modelp;
    bool __Vm_activity = false;  ///< Used by trace routines to determine change occurred
    uint32_t __Vm_baseCode = 0;  ///< Used by trace routines when tracing multiple models
    VlDeleter __Vm_deleter;
    bool __Vm_didInit = false;

    // MODULE INSTANCE STATE
    Vtop___024root                 TOP;
    Vtop_hwpe_stream_intf_stream__D20 TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o;
    Vtop_hwpe_stream_intf_stream__D20 TOP__hwpe_stream_buffer_wrapper__DOT__push_intf_i;
    Vtop_hwpe_stream_package       TOP__hwpe_stream_package;

    // SCOPE NAMES
    VerilatedScope __Vscope_TOP;
    VerilatedScope __Vscope_hwpe_stream_buffer_wrapper;
    VerilatedScope __Vscope_hwpe_stream_buffer_wrapper__i_hwpe_stream_buffer;
    VerilatedScope __Vscope_hwpe_stream_buffer_wrapper__i_hwpe_stream_buffer__i_cg;
    VerilatedScope __Vscope_hwpe_stream_buffer_wrapper__pop_intf_o;
    VerilatedScope __Vscope_hwpe_stream_buffer_wrapper__push_intf_i;
    VerilatedScope __Vscope_hwpe_stream_package;

    // SCOPE HIERARCHY
    VerilatedHierarchy __Vhier;

    // CONSTRUCTORS
    Vtop__Syms(VerilatedContext* contextp, const char* namep, Vtop* modelp);
    ~Vtop__Syms();

    // METHODS
    const char* name() { return TOP.name(); }
};

#endif  // guard
