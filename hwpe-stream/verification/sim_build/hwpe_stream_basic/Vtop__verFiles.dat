# DESCRIPTION: Verilator output: Timestamp data for --skip-identical.  Delete at will.
C "-cc --exe -Mdir /home/<USER>/asic_learn/hwpe-stream/verification/sim_build/hwpe_stream_basic -DCOCOTB_SIM=1 --top-module hwpe_stream_buffer_wrapper --vpi --public-flat-rw --prefix Vtop -o hwpe_stream_buffer_wrapper -LDFLAGS -Wl,-rpath,/home/<USER>/venv/eda_tool/lib/python3.12/site-packages/cocotb/libs -L/home/<USER>/venv/eda_tool/lib/python3.12/site-packages/cocotb/libs -lcocotbvpi_verilator --trace /home/<USER>/venv/eda_tool/lib/python3.12/site-packages/cocotb/share/lib/verilator/verilator.cpp /home/<USER>/asic_learn/hwpe-stream/rtl/hwpe_stream_package.sv /home/<USER>/asic_learn/hwpe-stream/rtl/hwpe_stream_interfaces.sv /home/<USER>/asic_learn/hwpe-stream/rtl/basic/hwpe_stream_buffer.sv /home/<USER>/asic_learn/tech_cells_generic/src/rtl/tc_clk.sv /home/<USER>/asic_learn/hwpe-stream/verification/hwpe_stream_buffer_wrapper.sv"
S      2143   139022  1750250968   422981025  1750250968   422981025 "/home/<USER>/asic_learn/hwpe-stream/rtl/basic/hwpe_stream_buffer.sv"
S      4159   139039  1748350358   880525196  1748350358   880525196 "/home/<USER>/asic_learn/hwpe-stream/rtl/hwpe_stream_interfaces.sv"
S      4047   139040  1750250140   211170876  1750250140   211170876 "/home/<USER>/asic_learn/hwpe-stream/rtl/hwpe_stream_package.sv"
S      2037   194555  1750254536   710210651  1750254536   710210651 "/home/<USER>/asic_learn/hwpe-stream/verification/hwpe_stream_buffer_wrapper.sv"
T      5766   121569  1750339747   619629091  1750339747   619629091 "/home/<USER>/asic_learn/hwpe-stream/verification/sim_build/hwpe_stream_basic/Vtop.cpp"
T      4197   121568  1750339747   619629091  1750339747   619629091 "/home/<USER>/asic_learn/hwpe-stream/verification/sim_build/hwpe_stream_basic/Vtop.h"
T      2334   121599  1750339747   623629090  1750339747   623629090 "/home/<USER>/asic_learn/hwpe-stream/verification/sim_build/hwpe_stream_basic/Vtop.mk"
T       669   121567  1750339747   619629091  1750339747   619629091 "/home/<USER>/asic_learn/hwpe-stream/verification/sim_build/hwpe_stream_basic/Vtop__Dpi.cpp"
T       520   121566  1750339747   619629091  1750339747   619629091 "/home/<USER>/asic_learn/hwpe-stream/verification/sim_build/hwpe_stream_basic/Vtop__Dpi.h"
T     16795   121564  1750339747   619629091  1750339747   619629091 "/home/<USER>/asic_learn/hwpe-stream/verification/sim_build/hwpe_stream_basic/Vtop__Syms.cpp"
T      2010   121565  1750339747   619629091  1750339747   619629091 "/home/<USER>/asic_learn/hwpe-stream/verification/sim_build/hwpe_stream_basic/Vtop__Syms.h"
T       290   121596  1750339747   623629090  1750339747   623629090 "/home/<USER>/asic_learn/hwpe-stream/verification/sim_build/hwpe_stream_basic/Vtop__TraceDecls__0__Slow.cpp"
T      5356   121597  1750339747   623629090  1750339747   623629090 "/home/<USER>/asic_learn/hwpe-stream/verification/sim_build/hwpe_stream_basic/Vtop__Trace__0.cpp"
T     20368   121595  1750339747   623629090  1750339747   623629090 "/home/<USER>/asic_learn/hwpe-stream/verification/sim_build/hwpe_stream_basic/Vtop__Trace__0__Slow.cpp"
T      3879   121571  1750339747   619629091  1750339747   619629091 "/home/<USER>/asic_learn/hwpe-stream/verification/sim_build/hwpe_stream_basic/Vtop___024root.h"
T      9262   121580  1750339747   623629090  1750339747   623629090 "/home/<USER>/asic_learn/hwpe-stream/verification/sim_build/hwpe_stream_basic/Vtop___024root__DepSet_h84412442__0.cpp"
T       845   121578  1750339747   619629091  1750339747   619629091 "/home/<USER>/asic_learn/hwpe-stream/verification/sim_build/hwpe_stream_basic/Vtop___024root__DepSet_h84412442__0__Slow.cpp"
T      7119   121585  1750339747   623629090  1750339747   623629090 "/home/<USER>/asic_learn/hwpe-stream/verification/sim_build/hwpe_stream_basic/Vtop___024root__DepSet_heccd7ead__0.cpp"
T      9439   121579  1750339747   623629090  1750339747   623629090 "/home/<USER>/asic_learn/hwpe-stream/verification/sim_build/hwpe_stream_basic/Vtop___024root__DepSet_heccd7ead__0__Slow.cpp"
T      1073   121577  1750339747   619629091  1750339747   619629091 "/home/<USER>/asic_learn/hwpe-stream/verification/sim_build/hwpe_stream_basic/Vtop___024root__Slow.cpp"
T       627   121572  1750339747   619629091  1750339747   619629091 "/home/<USER>/asic_learn/hwpe-stream/verification/sim_build/hwpe_stream_basic/Vtop___024unit.h"
T       467   121588  1750339747   623629090  1750339747   623629090 "/home/<USER>/asic_learn/hwpe-stream/verification/sim_build/hwpe_stream_basic/Vtop___024unit__DepSet_hff17caec__0__Slow.cpp"
T       620   121587  1750339747   623629090  1750339747   623629090 "/home/<USER>/asic_learn/hwpe-stream/verification/sim_build/hwpe_stream_basic/Vtop___024unit__Slow.cpp"
T       773   121570  1750339747   619629091  1750339747   619629091 "/home/<USER>/asic_learn/hwpe-stream/verification/sim_build/hwpe_stream_basic/Vtop__pch.h"
T      3526   121600  1750339747   623629090  1750339747   623629090 "/home/<USER>/asic_learn/hwpe-stream/verification/sim_build/hwpe_stream_basic/Vtop__ver.d"
T         0        0  1750339747   623629090  1750339747   623629090 "/home/<USER>/asic_learn/hwpe-stream/verification/sim_build/hwpe_stream_basic/Vtop__verFiles.dat"
T      2183   121598  1750339747   623629090  1750339747   623629090 "/home/<USER>/asic_learn/hwpe-stream/verification/sim_build/hwpe_stream_basic/Vtop_classes.mk"
T      1207   121573  1750339747   619629091  1750339747   619629091 "/home/<USER>/asic_learn/hwpe-stream/verification/sim_build/hwpe_stream_basic/Vtop_hwpe_stream_intf_stream__D20.h"
T       432   121592  1750339747   623629090  1750339747   623629090 "/home/<USER>/asic_learn/hwpe-stream/verification/sim_build/hwpe_stream_basic/Vtop_hwpe_stream_intf_stream__D20__DepSet_h3dd2c6a9__0.cpp"
T       754   121591  1750339747   623629090  1750339747   623629090 "/home/<USER>/asic_learn/hwpe-stream/verification/sim_build/hwpe_stream_basic/Vtop_hwpe_stream_intf_stream__D20__DepSet_h3dd2c6a9__0__Slow.cpp"
T      1152   121590  1750339747   623629090  1750339747   623629090 "/home/<USER>/asic_learn/hwpe-stream/verification/sim_build/hwpe_stream_basic/Vtop_hwpe_stream_intf_stream__D20__Slow.cpp"
T      1110   121575  1750339747   619629091  1750339747   619629091 "/home/<USER>/asic_learn/hwpe-stream/verification/sim_build/hwpe_stream_basic/Vtop_hwpe_stream_package.h"
T       511   121594  1750339747   623629090  1750339747   623629090 "/home/<USER>/asic_learn/hwpe-stream/verification/sim_build/hwpe_stream_basic/Vtop_hwpe_stream_package__DepSet_h7a78b85c__0__Slow.cpp"
T      1221   121593  1750339747   623629090  1750339747   623629090 "/home/<USER>/asic_learn/hwpe-stream/verification/sim_build/hwpe_stream_basic/Vtop_hwpe_stream_package__Slow.cpp"
S      3314   169715  1750255287   186043943  1750255287   186043943 "/home/<USER>/asic_learn/tech_cells_generic/src/rtl/tc_clk.sv"
S  16380528    96572  1747752339   292052954  1747752339   292052954 "/usr/local/bin/verilator_bin"
S      6525    96654  1747752339   568052902  1747752339   568052902 "/usr/local/share/verilator/include/verilated_std.sv"
S      2787    96636  1747752339   564052903  1747752339   564052903 "/usr/local/share/verilator/include/verilated_std_waiver.vlt"
