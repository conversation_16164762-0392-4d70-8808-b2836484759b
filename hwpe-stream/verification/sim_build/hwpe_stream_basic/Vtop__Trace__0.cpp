// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Tracing implementation internals
#include "verilated_vcd_c.h"
#include "Vtop__Syms.h"


void Vtop___024root__trace_chg_0_sub_0(Vtop___024root* vlSelf, VerilatedVcd::Buffer* bufp);

void Vtop___024root__trace_chg_0(void* voidSelf, VerilatedVcd::Buffer* bufp) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vtop___024root__trace_chg_0\n"); );
    // Init
    Vtop___024root* const __restrict vlSelf VL_ATTR_UNUSED = static_cast<Vtop___024root*>(voidSelf);
    Vtop__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    if (VL_UNLIKELY(!vlSymsp->__Vm_activity)) return;
    // Body
    Vtop___024root__trace_chg_0_sub_0((&vlSymsp->TOP), bufp);
}

void Vtop___024root__trace_chg_0_sub_0(Vtop___024root* vlSelf, VerilatedVcd::Buffer* bufp) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vtop___024root__trace_chg_0_sub_0\n"); );
    Vtop__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    auto& vlSelfRef = std::ref(*vlSelf).get();
    // Init
    uint32_t* const oldp VL_ATTR_UNUSED = bufp->oldp(vlSymsp->__Vm_baseCode + 1);
    // Body
    bufp->chgBit(oldp+0,(vlSelfRef.clk_i));
    bufp->chgBit(oldp+1,(vlSelfRef.rst_ni));
    bufp->chgBit(oldp+2,(vlSelfRef.clear_i));
    bufp->chgBit(oldp+3,(vlSelfRef.test_mode_i));
    bufp->chgBit(oldp+4,(vlSelfRef.push_i_valid));
    bufp->chgIData(oldp+5,(vlSelfRef.push_i_data),32);
    bufp->chgCData(oldp+6,(vlSelfRef.push_i_strb),4);
    bufp->chgBit(oldp+7,(vlSelfRef.push_i_ready));
    bufp->chgBit(oldp+8,(vlSelfRef.pop_o_valid));
    bufp->chgIData(oldp+9,(vlSelfRef.pop_o_data),32);
    bufp->chgCData(oldp+10,(vlSelfRef.pop_o_strb),4);
    bufp->chgBit(oldp+11,(vlSelfRef.pop_o_ready));
    bufp->chgBit(oldp+12,(vlSelfRef.hwpe_stream_buffer_wrapper__DOT__clk_i));
    bufp->chgBit(oldp+13,(vlSelfRef.hwpe_stream_buffer_wrapper__DOT__rst_ni));
    bufp->chgBit(oldp+14,(vlSelfRef.hwpe_stream_buffer_wrapper__DOT__clear_i));
    bufp->chgBit(oldp+15,(vlSelfRef.hwpe_stream_buffer_wrapper__DOT__test_mode_i));
    bufp->chgBit(oldp+16,(vlSelfRef.hwpe_stream_buffer_wrapper__DOT__push_i_valid));
    bufp->chgIData(oldp+17,(vlSelfRef.hwpe_stream_buffer_wrapper__DOT__push_i_data),32);
    bufp->chgCData(oldp+18,(vlSelfRef.hwpe_stream_buffer_wrapper__DOT__push_i_strb),4);
    bufp->chgBit(oldp+19,(vlSelfRef.hwpe_stream_buffer_wrapper__DOT__push_i_ready));
    bufp->chgBit(oldp+20,(vlSelfRef.hwpe_stream_buffer_wrapper__DOT__pop_o_valid));
    bufp->chgIData(oldp+21,(vlSelfRef.hwpe_stream_buffer_wrapper__DOT__pop_o_data),32);
    bufp->chgCData(oldp+22,(vlSelfRef.hwpe_stream_buffer_wrapper__DOT__pop_o_strb),4);
    bufp->chgBit(oldp+23,(vlSelfRef.hwpe_stream_buffer_wrapper__DOT__pop_o_ready));
    bufp->chgBit(oldp+24,(vlSelfRef.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__clk_i));
    bufp->chgBit(oldp+25,(vlSelfRef.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__rst_ni));
    bufp->chgBit(oldp+26,(vlSelfRef.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__clear_i));
    bufp->chgBit(oldp+27,(vlSelfRef.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__test_mode_i));
    bufp->chgBit(oldp+28,(vlSelfRef.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__clk_gated));
    bufp->chgBit(oldp+29,(vlSelfRef.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__i_cg__DOT__clk_i));
    bufp->chgBit(oldp+30,(vlSelfRef.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__i_cg__DOT__en_i));
    bufp->chgBit(oldp+31,(vlSelfRef.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__i_cg__DOT__test_en_i));
    bufp->chgBit(oldp+32,(vlSelfRef.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__i_cg__DOT__clk_o));
    bufp->chgBit(oldp+33,(vlSelfRef.hwpe_stream_buffer_wrapper__DOT__i_hwpe_stream_buffer__DOT__i_cg__DOT__clk_en));
    bufp->chgBit(oldp+34,(vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__push_intf_i.clk));
    bufp->chgBit(oldp+35,(vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__push_intf_i.valid));
    bufp->chgBit(oldp+36,(vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__push_intf_i.ready));
    bufp->chgIData(oldp+37,(vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__push_intf_i.data),32);
    bufp->chgCData(oldp+38,(vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__push_intf_i.strb),4);
    bufp->chgBit(oldp+39,(vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.clk));
    bufp->chgBit(oldp+40,(vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.valid));
    bufp->chgBit(oldp+41,(vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.ready));
    bufp->chgIData(oldp+42,(vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.data),32);
    bufp->chgCData(oldp+43,(vlSymsp->TOP__hwpe_stream_buffer_wrapper__DOT__pop_intf_o.strb),4);
}

void Vtop___024root__trace_cleanup(void* voidSelf, VerilatedVcd* /*unused*/) {
    VL_DEBUG_IF(VL_DBG_MSGF("+    Vtop___024root__trace_cleanup\n"); );
    // Init
    Vtop___024root* const __restrict vlSelf VL_ATTR_UNUSED = static_cast<Vtop___024root*>(voidSelf);
    Vtop__Syms* const __restrict vlSymsp VL_ATTR_UNUSED = vlSelf->vlSymsp;
    VlUnpacked<CData/*0:0*/, 1> __Vm_traceActivity;
    for (int __Vi0 = 0; __Vi0 < 1; ++__Vi0) {
        __Vm_traceActivity[__Vi0] = 0;
    }
    // Body
    vlSymsp->__Vm_activity = false;
    __Vm_traceActivity[0U] = 0U;
}
