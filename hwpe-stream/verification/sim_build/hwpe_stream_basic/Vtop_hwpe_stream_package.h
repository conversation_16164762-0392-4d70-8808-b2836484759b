// Verilated -*- C++ -*-
// DESCRIPTION: Verilator output: Design internal header
// See Vtop.h for the primary calling header

#ifndef VERILATED_VTOP_HWPE_STREAM_PACKAGE_H_
#define VERILATED_VTOP_HWPE_STREAM_PACKAGE_H_  // guard

#include "verilated.h"


class Vtop__Syms;

class alignas(VL_CACHE_LINE_BYTES) Vtop_hwpe_stream_package final : public VerilatedModule {
  public:

    // INTERNAL VARIABLES
    Vtop__Syms* const vlSymsp;

    // PARAMETERS
    static constexpr CData/*1:0*/ HWPE_STREAM_ADDRESSGEN_3D = 3U;
    static constexpr CData/*1:0*/ HWPE_STREAM_ADDRESSGEN_2D = 1U;
    static constexpr CData/*1:0*/ HWPE_STREAM_ADDRESSGEN_1D = 0U;
    static constexpr IData/*31:0*/ HWPE_STREAM_REALIGN_SOURCE = 0U;
    static constexpr IData/*31:0*/ HWPE_STREAM_REALIGN_SINK = 1U;
    static constexpr IData/*31:0*/ NB_SERDES_STREAMS_MAX = 0x00000400U;

    // CONSTRUCTORS
    Vtop_hwpe_stream_package(Vtop__Syms* symsp, const char* v__name);
    ~Vtop_hwpe_stream_package();
    VL_UNCOPYABLE(Vtop_hwpe_stream_package);

    // INTERNAL METHODS
    void __Vconfigure(bool first);
};


#endif  // guard
