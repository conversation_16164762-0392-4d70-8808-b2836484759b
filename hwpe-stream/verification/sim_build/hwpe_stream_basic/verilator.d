verilator.o: \
 /home/<USER>/venv/eda_tool/lib/python3.12/site-packages/cocotb/share/lib/verilator/verilator.cpp \
 Vtop.h /usr/local/share/verilator/include/verilated.h \
 /usr/local/share/verilator/include/verilated_config.h \
 /usr/local/share/verilator/include/verilatedos.h \
 /usr/local/share/verilator/include/verilated_types.h \
 /usr/local/share/verilator/include/verilated_funcs.h \
 /usr/local/share/verilator/include/vltstd/svdpi.h \
 /usr/local/share/verilator/include/verilated_vpi.h \
 /usr/local/share/verilator/include/verilated.h \
 /usr/local/share/verilator/include/verilated_syms.h \
 /usr/local/share/verilator/include/verilated_sym_props.h \
 /usr/local/share/verilator/include/vltstd/sv_vpi_user.h \
 /usr/local/share/verilator/include/vltstd/vpi_user.h \
 /usr/local/share/verilator/include/verilated_vcd_c.h \
 /usr/local/share/verilator/include/verilated_trace.h
