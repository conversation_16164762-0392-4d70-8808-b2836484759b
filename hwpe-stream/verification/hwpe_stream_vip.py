#!/usr/bin/env python3
"""
HWPE-Stream Verification IP (VIP) for cocotb
==============================================

This module provides a complete verification IP for the HWPE-Stream protocol,
implementing the transaction-level modeling approach described in the cocotb
documentation. The implementation follows the HWPE-Stream protocol specification
with proper valid/ready handshake handling.

Author: Generated for HWPE verification
License: See project LICENSE
"""

import cocotb
from cocotb.triggers import RisingEdge, ReadOnly, NextTimeStep
from cocotb.log import SimLog
from cocotb.queue import Queue

from dataclasses import dataclass, field
from typing import List, Optional, Callable, Any
import random


@dataclass
class HWPEStreamTransaction:
    """
    Represents a single transaction on the HWPE-Stream interface.
    
    Attributes:
        data: The data payload (integer representation)
        strb: Optional byte strobe signals (None means all bytes valid)
        data_width: Width of the data bus in bits
        metadata: Optional metadata for test control (delays, etc.)
    """
    data: int
    strb: Optional[int] = None
    data_width: int = 32
    metadata: dict = field(default_factory=dict)
    
    def __post_init__(self):
        """Initialize default strobe if not provided"""
        if self.strb is None:
            # Default strobe is all valid bytes
            num_bytes = self.data_width // 8
            self.strb = (1 << num_bytes) - 1
    
    def get_byte_enables(self) -> List[bool]:
        """Return list of boolean byte enables"""
        if self.strb is None:
            return []
        num_bytes = self.data_width // 8
        return [(self.strb >> i) & 1 == 1 for i in range(num_bytes)]
    
    def get_valid_bytes(self) -> List[int]:
        """Return list of valid data bytes"""
        bytes_list = []
        if self.strb is None:
            return bytes_list
        num_bytes = self.data_width // 8
        for i in range(num_bytes):
            if (self.strb >> i) & 1:
                byte_val = (self.data >> (i * 8)) & 0xFF
                bytes_list.append(byte_val)
            else:
                bytes_list.append(None)
        return bytes_list
    
    def __str__(self):
        return (f"HWPEStreamTransaction(data=0x{self.data:X}, "
                f"strb=0x{self.strb:X}, width={self.data_width})")


class HWPEStreamDriver:
    """
    Driver for HWPE-Stream interface (source side).
    
    This driver converts HWPEStreamTransaction objects into proper
    HWPE-Stream protocol signals with valid/ready handshake.
    """
    
    def __init__(self, entity, signal_prefix: str, clock, reset=None):
        """
        Initialize HWPE-Stream driver.
        
        Args:
            entity: DUT entity
            signal_prefix: Prefix for signal names (e.g., "stream_o")
            clock: Clock signal
            reset: Optional reset signal (active low)
        """
        self.log = SimLog(f"cocotb.{entity._name}.{signal_prefix}.HWPEStreamDriver")
        self.entity = entity
        self.signal_prefix = signal_prefix
        self.clock = clock
        self.reset = reset
        
        # Discover signals by naming convention
        try:
            self.valid = getattr(entity, f"{signal_prefix}_valid")
            self.ready = getattr(entity, f"{signal_prefix}_ready")
            self.data = getattr(entity, f"{signal_prefix}_data")
        except AttributeError as e:
            self.log.error(f"Failed to find required signals with prefix '{signal_prefix}': {e}")
            raise
        
        # Optional strobe signal
        try:
            self.strb = getattr(entity, f"{signal_prefix}_strb")
            self.has_strb = True
        except AttributeError:
            self.strb = None
            self.has_strb = False
            self.log.info(f"No strobe signal found for prefix '{signal_prefix}'")
        
        # Get data width from signal
        self.data_width = len(self.data)
        self.log.info(f"Initialized HWPE-Stream driver: data_width={self.data_width}, has_strb={self.has_strb}")
        
        # Initialize output signals
        self._reset_signals()
        # Internal queue and streaming process for back-to-back transactions
        self._tx_queue = Queue()
        cocotb.start_soon(self._streaming_proc())
    
    def _reset_signals(self):
        """Reset all output signals to safe values"""
        self.valid.value = 0
        self.data.value = 0
        if self.has_strb and self.strb is not None:
            self.strb.value = 0
    
    async def _wait_reset_deasserted(self):
        """Wait for reset to be deasserted if reset signal is provided"""
        if self.reset is not None:
            while self.reset.value == 0:
                await RisingEdge(self.clock)
    
    async def send(self, transaction: HWPEStreamTransaction):
        """
        Send a single transaction on the HWPE-Stream interface.
        
        Args:
            transaction: HWPEStreamTransaction to send
        """
        # Enqueue the transaction for the streaming process
        await self._tx_queue.put(transaction)
    
    async def _streaming_proc(self):
        """
        Streaming process to drive transactions per HWPE-Stream protocol.
        
        Rules strictly followed:
        1. Handshake occurs when both valid and ready are asserted
        2. Data can change only when valid is deasserted OR in cycle following handshake
        3. Valid assertion cannot depend combinationally on ready
        4. Valid deassertion can only happen in cycle after handshake
        """
        # Wait for reset deassertion
        await self._wait_reset_deasserted()
        
        while True:
            # Get next transaction (blocks until available)
            txn = await self._tx_queue.get()
            self.log.debug(f"Processing transaction: {txn}")
            
            # Wait for clock edge then drive signals (like register output)
            await RisingEdge(self.clock)
            
            # Assert valid and drive data (rule 3: valid assertion independent of ready)
            self.valid.value = 1
            self.data.value = txn.data
            if self.has_strb and self.strb is not None:
                self.strb.value = txn.strb
            
            # Wait for handshake completion
            while True:
                await RisingEdge(self.clock)
                await ReadOnly()  # Safe sampling
                
                if self.ready.value == 1:
                    # Handshake completed in this cycle
                    self.log.debug(f"Handshake completed for transaction: {txn}")
                    break
            
            # After handshake, move to next clock edge to avoid readonly phase
            await RisingEdge(self.clock) 
            
            # Now we're in the cycle following the handshake
            # Per rule 2: data can change now, per rule 4: valid can be deasserted now
            
            # Check if there are more transactions to send back-to-back
            if not self._tx_queue.empty():
                # Load next transaction (rule 2 allows data change after handshake)
                next_txn = await self._tx_queue.get()
                self.log.debug(f"Back-to-back transaction: {next_txn}")
                
                # Drive new data while keeping valid high (rule 2)
                self.data.value = next_txn.data
                if self.has_strb and self.strb is not None:
                    self.strb.value = next_txn.strb
                
                # Continue processing this new transaction
                txn = next_txn
                continue
            else:
                # No more transactions - deassert valid (rule 4)
                self.valid.value = 0


class HWPEStreamSink:
    """
    Sink-side driver for HWPE-Stream interface.
    
    This component can inject backpressure by controlling the ready signal,
    useful for testing source behavior under various ready patterns.
    """
    
    def __init__(self, entity, signal_prefix: str, clock, reset=None):
        """
        Initialize HWPE-Stream sink.
        
        Args:
            entity: DUT entity  
            signal_prefix: Prefix for signal names (e.g., "stream_i")
            clock: Clock signal
            reset: Optional reset signal (active low)
        """
        self.log = SimLog(f"cocotb.{entity._name}.{signal_prefix}.HWPEStreamSink")
        self.entity = entity
        self.signal_prefix = signal_prefix
        self.clock = clock
        self.reset = reset
        
        # Discover signals
        try:
            self.valid = getattr(entity, f"{signal_prefix}_valid")
            self.ready = getattr(entity, f"{signal_prefix}_ready") 
            self.data = getattr(entity, f"{signal_prefix}_data")
        except AttributeError as e:
            self.log.error(f"Failed to find required signals with prefix '{signal_prefix}': {e}")
            raise
        
        # Optional strobe signal
        try:
            self.strb = getattr(entity, f"{signal_prefix}_strb")
            self.has_strb = True
        except AttributeError:
            self.strb = None
            self.has_strb = False
        
        self.data_width = len(self.data)
        
        # Backpressure configuration
        self.backpressure_probability = 0.0  # 0.0 = never, 1.0 = always
        self.backpressure_cycles = []  # List of cycle counts for deterministic backpressure
        self.backpressure_index = 0
        
        # Initialize ready signal
        self.ready.value = 1
        
        self.log.info(f"Initialized HWPE-Stream sink: data_width={self.data_width}, has_strb={self.has_strb}")
    
    def set_backpressure_probability(self, probability: float):
        """Set random backpressure probability (0.0 to 1.0)"""
        self.backpressure_probability = max(0.0, min(1.0, probability))
        self.log.info(f"Set backpressure probability to {self.backpressure_probability}")
    
    def set_backpressure_pattern(self, cycle_counts: List[int]):
        """
        Set deterministic backpressure pattern.
        
        Args:
            cycle_counts: List of cycle counts. Even indices are ready cycles,
                         odd indices are not-ready cycles.
        """
        self.backpressure_cycles = cycle_counts
        self.backpressure_index = 0
        self.log.info(f"Set backpressure pattern: {cycle_counts}")
    
    async def start_backpressure_generation(self):
        """Start the backpressure generation coroutine"""
        cocotb.start_soon(self._backpressure_generator())
    
    async def _backpressure_generator(self):
        """Generate backpressure according to configured pattern"""
        while True:
            await RisingEdge(self.clock)
            
            if self.reset is not None and self.reset.value == 0:
                self.ready.value = 1
                continue
            
            if self.backpressure_cycles:
                # Deterministic pattern mode
                if self.backpressure_index < len(self.backpressure_cycles):
                    cycles = self.backpressure_cycles[self.backpressure_index]
                    is_ready_phase = (self.backpressure_index % 2) == 0
                    
                    self.ready.value = 1 if is_ready_phase else 0
                    
                    # Wait for the specified number of cycles
                    for _ in range(cycles - 1):  # -1 because we already had one cycle
                        await RisingEdge(self.clock)
                    
                    self.backpressure_index = (self.backpressure_index + 1) % len(self.backpressure_cycles)
                else:
                    self.ready.value = 1
            else:
                # Random mode
                if random.random() < self.backpressure_probability:
                    self.ready.value = 0
                else:
                    self.ready.value = 1


class HWPEStreamMonitor:
    """
    Monitor for HWPE-Stream interface.
    
    This monitor passively observes the HWPE-Stream interface and reconstructs
    transactions when valid handshakes occur.
    """
    
    def __init__(self, entity, signal_prefix: str, clock, callback: Optional[Callable] = None):
        """
        Initialize HWPE-Stream monitor.
        
        Args:
            entity: DUT entity
            signal_prefix: Prefix for signal names  
            clock: Clock signal
            callback: Optional callback function for received transactions
        """
        self.log = SimLog(f"cocotb.{entity._name}.{signal_prefix}.HWPEStreamMonitor")
        self.entity = entity
        self.signal_prefix = signal_prefix
        self.clock = clock
        self.callback = callback
        
        # Discover signals
        try:
            self.valid = getattr(entity, f"{signal_prefix}_valid")
            self.ready = getattr(entity, f"{signal_prefix}_ready")
            self.data = getattr(entity, f"{signal_prefix}_data")
        except AttributeError as e:
            self.log.error(f"Failed to find required signals with prefix '{signal_prefix}': {e}")
            raise
        
        # Optional strobe signal
        try:
            self.strb = getattr(entity, f"{signal_prefix}_strb")
            self.has_strb = True
        except AttributeError:
            self.strb = None
            self.has_strb = False
        
        self.data_width = len(self.data)
        self.log.info(f"Initialized HWPE-Stream monitor: data_width={self.data_width}, has_strb={self.has_strb}")
        
        # Statistics
        self.transaction_count = 0
        self.cycle_count = 0
        
        # Transaction queue for scoreboard
        self.received_transactions = Queue()
    
    async def start_monitoring(self):
        """Start the monitoring coroutine"""
        cocotb.start_soon(self._monitor_recv())
    
    async def _monitor_recv(self):
        """
        Main monitoring coroutine that watches for valid handshakes.
        """
        prev_data = None
        prev_strb = None
        
        while True:
            await RisingEdge(self.clock)
            self.cycle_count += 1
            
            # Sample signals in read-only phase to avoid race conditions  
            await ReadOnly()
            
            # Check for valid handshake
            current_valid = int(self.valid.value)
            current_ready = int(self.ready.value)
            
            if current_valid == 1 and current_ready == 1:
                # Capture transaction data
                data_val = int(self.data.value)
                strb_val = None
                
                if self.has_strb and self.strb is not None:
                    strb_val = int(self.strb.value)
                
                # Only record if data/strb has changed from previous transaction
                # This prevents double-counting when the same data persists across cycles
                if data_val != prev_data or strb_val != prev_strb:
                    # Create transaction object
                    transaction = HWPEStreamTransaction(
                        data=data_val,
                        strb=strb_val,
                        data_width=self.data_width,
                        metadata={'cycle': self.cycle_count}
                    )
                    
                    self.transaction_count += 1
                    self.log.debug(f"Monitored transaction #{self.transaction_count}: {transaction}")
                    
                    # Send transaction to callback if provided
                    if self.callback:
                        self.callback(transaction)
                    
                    # Add to queue for scoreboard
                    await self.received_transactions.put(transaction)
                    
                    # Update previous values
                    prev_data = data_val
                    prev_strb = strb_val
    
    def get_statistics(self) -> dict:
        """Get monitoring statistics"""
        return {
            'transaction_count': self.transaction_count,
            'cycle_count': self.cycle_count,
            'throughput': self.transaction_count / max(1, self.cycle_count)
        }


class HWPEStreamSequencer:
    """
    High-level sequencer for generating HWPE-Stream transactions.
    
    This class provides convenient methods for generating common
    test patterns and sequences.
    """
    
    def __init__(self, data_width: int = 32):
        """
        Initialize sequencer.
        
        Args:
            data_width: Width of data bus in bits
        """
        self.data_width = data_width
        self.log = SimLog("HWPEStreamSequencer")
    
    def create_random_transaction(self, strb_pattern: Optional[str] = None) -> HWPEStreamTransaction:
        """
        Create a random transaction.
        
        Args:
            strb_pattern: Optional strobe pattern ('all', 'random', or None for all valid)
            
        Returns:
            Random HWPEStreamTransaction
        """
        data = random.randint(0, (1 << self.data_width) - 1)
        
        strb = None
        if strb_pattern == 'random':
            num_bytes = self.data_width // 8
            strb = random.randint(0, (1 << num_bytes) - 1)
        elif strb_pattern == 'all':
            num_bytes = self.data_width // 8  
            strb = (1 << num_bytes) - 1
        
        return HWPEStreamTransaction(data=data, strb=strb, data_width=self.data_width)
    
    def create_counting_sequence(self, length: int, start_value: int = 0) -> List[HWPEStreamTransaction]:
        """
        Create a sequence of transactions with counting data.
        
        Args:
            length: Number of transactions
            start_value: Starting value for counting
            
        Returns:
            List of HWPEStreamTransaction objects
        """
        transactions = []
        for i in range(length):
            data = (start_value + i) & ((1 << self.data_width) - 1)
            transactions.append(HWPEStreamTransaction(data=data, data_width=self.data_width))
        return transactions
    
    def create_burst_sequence(self, burst_length: int, inter_burst_delay: int = 0) -> List[HWPEStreamTransaction]:
        """
        Create a burst sequence with optional delays between transactions.
        
        Args:
            burst_length: Number of transactions in burst
            inter_burst_delay: Clock cycles delay between transactions
            
        Returns:
            List of HWPEStreamTransaction objects with delay metadata
        """
        transactions = []
        for i in range(burst_length):
            metadata = {}
            if i > 0 and inter_burst_delay > 0:
                metadata['pre_delay'] = inter_burst_delay
            
            data = random.randint(0, (1 << self.data_width) - 1)
            transaction = HWPEStreamTransaction(
                data=data, 
                data_width=self.data_width,
                metadata=metadata
            )
            transactions.append(transaction)
        return transactions


# Convenience functions for quick setup
def create_hwpe_stream_driver(dut, signal_prefix: str, clock) -> HWPEStreamDriver:
    """Create and return an HWPE-Stream driver"""
    return HWPEStreamDriver(dut, signal_prefix, clock)

def create_hwpe_stream_monitor(dut, signal_prefix: str, clock, callback=None) -> HWPEStreamMonitor:
    """Create and return an HWPE-Stream monitor"""
    return HWPEStreamMonitor(dut, signal_prefix, clock, callback)

def create_hwpe_stream_sink(dut, signal_prefix: str, clock) -> HWPEStreamSink:
    """Create and return an HWPE-Stream sink"""
    return HWPEStreamSink(dut, signal_prefix, clock) 