/*
 * hwpe_stream_buffer_wrapper.sv
 * Wrapper for hwpe_stream_buffer to convert interfaces to plain signals
 * for cocotb simulation compatibility
 */

import hwpe_stream_package::*;
module hwpe_stream_buffer_wrapper #(
  parameter int unsigned DATA_WIDTH = 32,
  parameter int unsigned STRB_WIDTH = DATA_WIDTH/8
)
(
  input  logic clk_i,
  input  logic rst_ni,
  input  logic clear_i,
  input  logic test_mode_i,

  // Push interface (sink) - expanded signals
  input  logic                     push_i_valid,
  input  logic [DATA_WIDTH-1:0]    push_i_data,
  input  logic [STRB_WIDTH-1:0]    push_i_strb,
  output logic                     push_i_ready,

  // Pop interface (source) - expanded signals  
  output logic                     pop_o_valid,
  output logic [DATA_WIDTH-1:0]    pop_o_data,
  output logic [STRB_WIDTH-1:0]    pop_o_strb,
  input  logic                     pop_o_ready
);

  // Create interface instances
  hwpe_stream_intf_stream #(
    .DATA_WIDTH(DATA_WIDTH)
  ) push_intf_i (
    .clk(clk_i)
  );

  hwpe_stream_intf_stream #(
    .DATA_WIDTH(DATA_WIDTH)
  ) pop_intf_o (
    .clk(clk_i)
  );

  // Connect interface signals to module ports (sink side - input to buffer)
  assign push_intf_i.valid = push_i_valid;
  assign push_intf_i.data  = push_i_data;
  assign push_intf_i.strb  = push_i_strb;
  assign push_i_ready      = push_intf_i.ready;

  // Connect interface signals to module ports (source side - output from buffer)
  assign pop_o_valid       = pop_intf_o.valid;
  assign pop_o_data        = pop_intf_o.data;
  assign pop_o_strb        = pop_intf_o.strb;
  assign pop_intf_o.ready  = pop_o_ready;

  // Instantiate the original hwpe_stream_buffer module
  hwpe_stream_buffer #(
    .DATA_WIDTH(DATA_WIDTH)
  ) i_hwpe_stream_buffer (
    .clk_i      ( clk_i        ),
    .rst_ni     ( rst_ni       ),
    .clear_i    ( clear_i      ),
    .test_mode_i( test_mode_i  ),
    .push_i     ( push_intf_i.sink   ),
    .pop_o      ( pop_intf_o.source  )
  );

endmodule // hwpe_stream_buffer_wrapper 