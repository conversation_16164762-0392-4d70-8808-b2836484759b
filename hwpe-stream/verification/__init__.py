"""
HWPE-Stream Verification IP Package
===================================

This package provides a complete cocotb-based verification IP for the HWPE-Stream protocol.

Main Components:
- HWPEStreamTransaction: Transaction-level representation
- HWPEStreamDriver: Source-side driver
- HWPEStreamMonitor: Passive monitor
- HWPEStreamSink: Sink-side driver with backpressure support
- HWPEStreamSequencer: High-level sequence generator

Usage:
    from hwpe_stream_vip import HWPEStreamDriver, HWPEStreamTransaction
    
    # Create and use VIP components in your cocotb tests
"""

from .hwpe_stream_vip import (
    HWPEStreamTransaction,
    HWPEStreamDriver, 
    HWPEStreamMonitor,
    HWPEStreamSink,
    HWPEStreamSequencer,
    create_hwpe_stream_driver,
    create_hwpe_stream_monitor,
    create_hwpe_stream_sink
)

__version__ = "1.0.0"
__author__ = "HWPE Verification Team"

__all__ = [
    "HWPEStreamTransaction",
    "HWPEStreamDriver",
    "HWPEStreamMonitor", 
    "HWPEStreamSink",
    "HWPEStreamSequencer",
    "create_hwpe_stream_driver",
    "create_hwpe_stream_monitor",
    "create_hwpe_stream_sink"
] 