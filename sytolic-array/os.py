import numpy as np
from collections import deque

class PE_OS:
    """输出固定（OS）处理单元"""
    def __init__(self):
        self.psum_stationary_reg = 0  # 部分和固定在PE内部
        self.input_reg = 0
        self.weight_reg = 0
        self.input_out = 0
        self.weight_out = 0

    def compute(self):
        """执行乘加操作，更新固定的部分和"""
        self.psum_stationary_reg += self.input_reg * self.weight_reg

    def tick(self):
        """模拟一个时钟周期的数据传递"""
        self.input_out = self.input_reg
        self.weight_out = self.weight_reg
        
    def drain(self):
        """排出最终结果"""
        return self.psum_stationary_reg

class SystolicArray_OS:
    """输出固定（OS）脉动阵列"""
    def __init__(self, size):
        self.size = size
        self.pes = for _ in range(size)]
        # 为阵列的顶部和左侧边缘创建输入FIFO
        self.input_fifos = [deque() for _ in range(size)] # 顶部输入 (A)
        self.weight_fifos = [deque() for _ in range(size)] # 左侧输入 (B)

    def tick(self):
        """驱动整个阵列进行一个时钟周期的计算和数据传递"""
        # 1. 从FIFO读取数据到边缘PE
        for c in range(self.size):
            self.pes[c].input_reg = self.input_fifos[c].popleft() if self.input_fifos[c] else 0
        for r in range(self.size):
            self.pes[r].weight_reg = self.weight_fifos[r].popleft() if self.weight_fifos[r] else 0

        # 2. 在PE内部进行数据传递
        # 输入(A)从上到下传递
        for c in range(self.size):
            for r in range(1, self.size):
                self.pes[r][c].input_reg = self.pes[r-1][c].input_out
        # 权重(B)从左到右传递
        for r in range(self.size):
            for c in range(1, self.size):
                self.pes[r][c].weight_reg = self.pes[r][c-1].weight_out
        
        # 3. 所有PE执行计算
        for r in range(self.size):
            for c in range(self.size):
                self.pes[r][c].compute()

        # 4. 所有PE更新其输出值
        for r in range(self.size):
            for c in range(self.size):
                self.pes[r][c].tick()

    def drain_results(self):
        """从所有PE中排出最终结果"""
        result_matrix = np.zeros((self.size, self.size), dtype=int)
        for r in range(self.size):
            for c in range(self.size):
                result_matrix[r, c] = self.pes[r][c].drain()
        return result_matrix

# --- 仿真与验证 ---
if __name__ == "__main__":
    N = 3
    # 创建输入矩阵 A (Inputs) 和 B (Weights)
    matrix_A = np.arange(1, N*N + 1).reshape(N, N)
    matrix_B = np.arange(N*N + 1, 2*N*N + 1).reshape(N, N)

    # 准备偏置后的输入数据流
    # 输入A (inputs) 从顶部进入，按列送入，每列有偏置
    inputs_A_skewed = [deque(*c + list(matrix_A[:, c]) + *(N-1-c)) for c in range(N)]
    # 权重B (weights) 从左侧进入，按行送入，每行有偏置
    weights_B_skewed = [deque(*r + list(matrix_B[r, :]) + *(N-1-r)) for r in range(N)]

    # 创建并初始化脉动阵列
    sa_os = SystolicArray_OS(N)
    sa_os.input_fifos = inputs_A_skewed
    sa_os.weight_fifos = weights_B_skewed

    # 运行仿真
    num_cycles = N + N + N - 2 # 填充和计算的总周期
    for _ in range(num_cycles):
        sa_os.tick()

    # 排出结果并验证
    result_C = sa_os.drain_results()
    golden_result = np.matmul(matrix_A, matrix_B)
    print("Systolic Array (OS) Result:\n", result_C)
    print("\nNumPy Golden Result:\n", golden_result)
    assert np.array_equal(result_C, golden_result)
    print("\nVerification Successful!")