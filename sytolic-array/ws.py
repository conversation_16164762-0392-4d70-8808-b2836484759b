import numpy as np
from collections import deque

class PE_WS:
    """权重固定（WS）处理单元"""
    def __init__(self):
        self.weight_reg = 0
        self.input_reg = 0
        self.psum_reg = 0
        self.input_out = 0
        self.psum_out = 0

    def compute(self):
        """执行乘加操作"""
        self.psum_reg += self.input_reg * self.weight_reg

    def tick(self):
        """模拟一个时钟周期的数据传递"""
        self.input_out = self.input_reg
        self.psum_out = self.psum_reg

class SystolicArray_WS:
    """权重固定（WS）脉动阵列"""
    def __init__(self, size):
        self.size = size
        self.pes = [[PE_WS() for _ in range(size)] for _ in range(size)]
        # 为阵列的顶部和左侧边缘创建输入FIFO
        self.input_fifos = [deque() for _ in range(size)]
        self.psum_fifos = [deque() for _ in range(size)]
        self.output_fifos = [deque() for _ in range(size)]

    def preload_weights(self, weights):
        """预加载权重到每个PE"""
        for r in range(self.size):
            for c in range(self.size):
                self.pes[r][c].weight_reg = weights[r, c]

    def tick(self):
        """驱动整个阵列进行一个时钟周期的计算和数据传递"""
        # 1. 所有PE执行计算（基于当前寄存器值）
        for r in range(self.size):
            for c in range(self.size):
                self.pes[r][c].compute()
        
        # 2. 所有PE更新其输出值
        for r in range(self.size):
            for c in range(self.size):
                self.pes[r][c].tick()

        # 3. 在PE内部进行数据传递（从左到右，从上到下）
        for r in range(self.size):
            for c in range(1, self.size):
                self.pes[r][c].input_reg = self.pes[r][c-1].input_out
        for r in range(1, self.size):
            for c in range(self.size):
                self.pes[r][c].psum_reg = self.pes[r-1][c].psum_out

        # 4. 从FIFO读取数据到边缘PE的输入寄存器
        for r in range(self.size):
            self.pes[r][0].input_reg = self.input_fifos[r].popleft() if self.input_fifos[r] else 0
        for c in range(self.size):
            self.pes[0][c].psum_reg = self.psum_fifos[c].popleft() if self.psum_fifos[c] else 0

        # 5. 从底部边缘收集输出
        for c in range(self.size):
            self.output_fifos[c].append(self.pes[self.size-1][c].psum_out)

# --- 仿真与验证 ---
if __name__ == "__main__":
    N = 3
    # 创建输入矩阵 A (Inputs) 和 B (Weights)
    matrix_A = np.arange(1, N*N + 1).reshape(N, N)
    matrix_B = np.arange(N*N + 1, 2*N*N + 1).reshape(N, N)
    
    # 准备输入数据流（需要转置A并进行偏置填充）
    inputs_A_skewed = []
    for r in range(N):
        row = list(matrix_A[r])
        padding = [0] * r
        inputs_A_skewed.append(deque(padding + row))

    # 初始化部分和为0
    psums_in = [deque([0] * (N + N - 1)) for _ in range(N)]

    # 创建并初始化脉动阵列
    sa_ws = SystolicArray_WS(N)
    sa_ws.preload_weights(matrix_B) # WS: 预加载权重
    sa_ws.input_fifos = inputs_A_skewed
    sa_ws.psum_fifos = psums_in

    # 运行仿真
    num_cycles = N + N - 1 + N # 填充、计算和流出的总周期
    for cycle in range(num_cycles):
        # 填充剩余的输入FIFO
        for r in range(N):
            if not sa_ws.input_fifos[r]:
                sa_ws.input_fifos[r].append(0)
        sa_ws.tick()

    # 提取并整理输出结果
    result_C = np.zeros((N, N), dtype=int)
    for c in range(N):
        # 结果在特定周期后才有效
        valid_outputs = list(sa_ws.output_fifos[c])[N-1+c : N-1+c+N]
        for r in range(N):
            result_C[r, c] = valid_outputs[r]

    # 验证
    golden_result = np.matmul(matrix_A, matrix_B)
    print("Systolic Array (WS) Result:\n", result_C)
    print("\nNumPy Golden Result:\n", golden_result)
    assert np.array_equal(result_C, golden_result)
    print("\nVerification Successful!")